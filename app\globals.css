@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 47.4% 11.2%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;
    --radius: 0.5rem;

    /* Futuristic color variables */
    --neon-cyan: 186 100% 69%;
    --neon-purple: 280 100% 70%;
    --neon-pink: 340 100% 70%;
    --glass-bg: rgba(255, 255, 255, 0.05);
    --glass-border: rgba(134, 239, 172, 0.2);
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-family: "Inter", system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial,
      sans-serif;
    overflow-x: hidden;
  }
}

/* ============================================ */
/* FUTURISTIC STYLING & ANIMATIONS */
/* ============================================ */

/* Smooth scrolling with better easing */
html {
  scroll-behavior: smooth;
}

/* Enhanced scrollbar with neon effect */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(45deg, #06b6d4, #8b5cf6);
  border-radius: 4px;
  box-shadow: 0 0 10px rgba(6, 182, 212, 0.5);
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(45deg, #0891b2, #7c3aed);
  box-shadow: 0 0 15px rgba(6, 182, 212, 0.8);
}

/* Global smooth transitions with better timing */
* {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Enhanced focus styles for accessibility */
button:focus-visible,
a:focus-visible {
  outline: 2px solid #06b6d4;
  outline-offset: 2px;
  box-shadow: 0 0 20px rgba(6, 182, 212, 0.4);
}

/* ============================================ */
/* NEON GLOW EFFECTS */
/* ============================================ */

.neon-glow-cyan {
  box-shadow: 0 0 5px rgba(6, 182, 212, 0.5), 0 0 10px rgba(6, 182, 212, 0.3), 0 0 15px rgba(6, 182, 212, 0.2);
}

.neon-glow-purple {
  box-shadow: 0 0 5px rgba(139, 92, 246, 0.5), 0 0 10px rgba(139, 92, 246, 0.3), 0 0 15px rgba(139, 92, 246, 0.2);
}

.neon-glow-pink {
  box-shadow: 0 0 5px rgba(236, 72, 153, 0.5), 0 0 10px rgba(236, 72, 153, 0.3), 0 0 15px rgba(236, 72, 153, 0.2);
}

/* Text glow effects */
.text-glow-cyan {
  text-shadow: 0 0 10px rgba(6, 182, 212, 0.8), 0 0 20px rgba(6, 182, 212, 0.4), 0 0 30px rgba(6, 182, 212, 0.2);
}

.text-glow-purple {
  text-shadow: 0 0 10px rgba(139, 92, 246, 0.8), 0 0 20px rgba(139, 92, 246, 0.4), 0 0 30px rgba(139, 92, 246, 0.2);
}

/* ============================================ */
/* GLASSMORPHISM EFFECTS */
/* ============================================ */

.glass {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.glass-strong {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(30px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

/* ============================================ */
/* ENHANCED ANIMATIONS */
/* ============================================ */

/* Pulse animation with neon effect */
@keyframes neon-pulse {
  0%,
  100% {
    opacity: 1;
    box-shadow: 0 0 5px rgba(6, 182, 212, 0.5), 0 0 10px rgba(6, 182, 212, 0.3), 0 0 15px rgba(6, 182, 212, 0.2);
  }
  50% {
    opacity: 0.8;
    box-shadow: 0 0 10px rgba(6, 182, 212, 0.8), 0 0 20px rgba(6, 182, 212, 0.5), 0 0 30px rgba(6, 182, 212, 0.3);
  }
}

.animate-neon-pulse {
  animation: neon-pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Floating animation with rotation */
@keyframes float-rotate {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
  }
  33% {
    transform: translateY(-10px) rotate(120deg);
  }
  66% {
    transform: translateY(5px) rotate(240deg);
  }
}

.floating-rotate {
  animation: float-rotate 8s ease-in-out infinite;
}

/* Gradient animation for backgrounds */
@keyframes gradient-shift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.gradient-animate {
  background: linear-gradient(-45deg, #06b6d4, #8b5cf6, #ec4899, #10b981);
  background-size: 400% 400%;
  animation: gradient-shift 15s ease infinite;
}

/* Breathing glow effect */
@keyframes breathing-glow {
  0%,
  100% {
    box-shadow: 0 0 20px rgba(6, 182, 212, 0.3);
  }
  50% {
    box-shadow: 0 0 40px rgba(6, 182, 212, 0.6);
  }
}

.breathing-glow {
  animation: breathing-glow 3s ease-in-out infinite;
}

/* ============================================ */
/* ENHANCED HOVER EFFECTS */
/* ============================================ */

/* Card hover with multiple effects */
.card-hover-enhanced {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.card-hover-enhanced:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2), 0 0 20px rgba(6, 182, 212, 0.3);
}

/* Button hover with glow */
.btn-glow-hover {
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.btn-glow-hover::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.btn-glow-hover:hover::before {
  left: 100%;
}

.btn-glow-hover:hover {
  box-shadow: 0 5px 20px rgba(6, 182, 212, 0.4), 0 0 20px rgba(6, 182, 212, 0.2);
  transform: translateY(-2px);
}

/* ============================================ */
/* LOADING AND SKELETON ANIMATIONS */
/* ============================================ */

/* Enhanced loading animation */
@keyframes cyber-pulse {
  0%,
  100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.5;
    transform: scale(1.05);
  }
}

.animate-cyber-pulse {
  animation: cyber-pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Shimmer effect for loading states */
@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.shimmer {
  background: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0.05) 0px,
    rgba(255, 255, 255, 0.1) 40px,
    rgba(255, 255, 255, 0.05) 80px
  );
  background-size: 200px 100%;
  animation: shimmer 1.5s infinite;
}

/* ============================================ */
/* RESPONSIVE DESIGN IMPROVEMENTS */
/* ============================================ */

/* Mobile responsive adjustments */
@media (max-width: 768px) {
  .text-5xl {
    font-size: 2.5rem;
    line-height: 1.2;
  }

  .text-7xl {
    font-size: 3.5rem;
    line-height: 1.1;
  }

  /* Adjust card spacing on mobile */
  .card-hover-enhanced:hover {
    transform: translateY(-4px) scale(1.01);
  }

  /* Smaller glow effects on mobile for performance */
  .neon-glow-cyan,
  .neon-glow-purple,
  .neon-glow-pink {
    box-shadow: 0 0 3px rgba(6, 182, 212, 0.4), 0 0 6px rgba(6, 182, 212, 0.2);
  }
}

@media (max-width: 480px) {
  /* Further mobile optimizations */
  .glass,
  .glass-strong {
    backdrop-filter: blur(10px);
  }

  /* Disable complex animations on very small screens */
  .floating-rotate {
    animation: none;
  }
}

/* ============================================ */
/* ACCESSIBILITY IMPROVEMENTS */
/* ============================================ */

/* Reduced motion for users who prefer it */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }

  .floating-rotate,
  .animate-neon-pulse,
  .gradient-animate,
  .breathing-glow {
    animation: none;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .glass,
  .glass-strong {
    background: rgba(0, 0, 0, 0.8);
    border: 2px solid #06b6d4;
  }

  .neon-glow-cyan,
  .neon-glow-purple,
  .neon-glow-pink {
    box-shadow: none;
    border: 2px solid currentColor;
  }
}

/* ============================================ */
/* PRINT STYLES */
/* ============================================ */

@media print {
  .no-print,
  .floating-rotate,
  .animate-neon-pulse,
  .gradient-animate,
  .breathing-glow {
    display: none !important;
  }

  .glass,
  .glass-strong {
    background: white;
    border: 1px solid black;
    box-shadow: none;
  }

  .text-white {
    color: black !important;
  }

  .bg-gradient-to-r {
    background: linear-gradient(to right, #000, #333) !important;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }
}

/* ============================================ */
/* UTILITY CLASSES */
/* ============================================ */

/* Quick utility for centering with flex */
.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Text selection styling */
::selection {
  background-color: rgba(6, 182, 212, 0.3);
  color: white;
}

::-moz-selection {
  background-color: rgba(6, 182, 212, 0.3);
  color: white;
}

/* Custom cursor for interactive elements */
.cursor-cyber {
  cursor: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20"><circle cx="10" cy="10" r="8" fill="none" stroke="%2306b6d4" stroke-width="2"/></svg>'),
    auto;
}
