"use client"

import { useState, useEffect } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import {
  Palette,
  Code,
  Video,
  Lightbulb,
  Github,
  Linkedin,
  Mail,
  ExternalLink,
  Download,
  Moon,
  Sun,
  Sparkles,
  Heart,
  Coffee,
  Music,
  Camera,
  Figma,
  Instagram,
  MapPin,
  Briefcase,
  GraduationCap,
  Star,
  Eye,
  ChevronDown,
  Menu,
  X,
  Trophy,
  GitBranch,
  Calendar,
  Users,
  Target,
  Zap,
} from "lucide-react"

// ============================================
// PERSONAL INFORMATION - EASY TO CUSTOMIZE
// ============================================
const PERSONAL_INFO = {
  name: "<PERSON><PERSON><PERSON> Ava<PERSON>nu<PERSON>",
  title: "Full-Stack Developer • AI/ML Engineer • UI/UX Designer",
  bio: "Final-year B.Tech CSE (AI/ML) student with hands-on experience in full-stack development, software engineering, and GenAI/AI-ML applications. Skilled in designing and deploying scalable web apps, intelligent systems, and user-friendly interfaces using modern tech stacks. Seeking roles in software engineering, full stack, or AI/GenAI domains to deliver impactful solutions.",
  location: "Faridabad, Haryana, India",
  email: "<EMAIL>",
  phone: "+91-9958582766",
  developerResumeLink: "#", // Add your developer resume link here
  designerResumeLink: "#", // Add your designer resume link here
  profileImage: "/placeholder.svg?height=200&width=200", // Replace with your photo URL
}

// ============================================
// SOCIAL LINKS - UPDATE WITH YOUR PROFILES
// ============================================
const SOCIAL_LINKS = {
  github: "https://github.com/Manasvi0109",
  gitlab: "https://gitlab.com/manasvi0109",
  linkedin: "https://linkedin.com/in/manasviavadhanula",
  instagram: "https://instagram.com/manasvi_avadhanula",
  email: "mailto:<EMAIL>",
}

// ============================================
// SKILLS SECTION - ADD YOUR SKILLS
// ============================================
const SKILLS = [
  // Programming Languages
  { name: "Python", icon: Code, color: "from-blue-400 to-blue-600", level: 90, category: "Programming" },
  { name: "JavaScript", icon: Code, color: "from-yellow-400 to-orange-500", level: 88, category: "Programming" },
  { name: "Java", icon: Code, color: "from-red-400 to-red-600", level: 82, category: "Programming" },
  { name: "C++", icon: Code, color: "from-purple-400 to-purple-600", level: 80, category: "Programming" },

  // Web Development
  { name: "React", icon: Code, color: "from-cyan-400 to-blue-500", level: 92, category: "Web Development" },
  { name: "Node.js", icon: Code, color: "from-green-400 to-green-600", level: 88, category: "Web Development" },
  { name: "Django", icon: Code, color: "from-green-600 to-green-800", level: 85, category: "Web Development" },
  { name: "HTML/CSS", icon: Code, color: "from-orange-400 to-red-500", level: 95, category: "Web Development" },
  { name: "REST APIs", icon: Code, color: "from-indigo-400 to-purple-500", level: 87, category: "Web Development" },

  // Databases
  { name: "MySQL", icon: Code, color: "from-blue-500 to-blue-700", level: 85, category: "Database" },
  { name: "MongoDB", icon: Code, color: "from-green-500 to-green-700", level: 83, category: "Database" },
  { name: "PostgreSQL", icon: Code, color: "from-blue-600 to-indigo-600", level: 80, category: "Database" },

  // AI/ML
  { name: "Scikit-learn", icon: Lightbulb, color: "from-orange-400 to-orange-600", level: 88, category: "AI/ML" },
  { name: "NumPy", icon: Lightbulb, color: "from-blue-400 to-blue-600", level: 90, category: "AI/ML" },
  { name: "Pandas", icon: Lightbulb, color: "from-purple-400 to-purple-600", level: 92, category: "AI/ML" },
  {
    name: "GenAI & Prompt Engineering",
    icon: Lightbulb,
    color: "from-pink-400 to-rose-500",
    level: 89,
    category: "AI/ML",
  },
  { name: "Deep Learning", icon: Lightbulb, color: "from-violet-400 to-purple-500", level: 82, category: "AI/ML" },

  // Design Tools
  { name: "Figma", icon: Figma, color: "from-purple-400 to-pink-500", level: 93, category: "Design" },
  { name: "Adobe Premiere Pro", icon: Video, color: "from-purple-600 to-blue-600", level: 91, category: "Design" },
  { name: "After Effects", icon: Video, color: "from-blue-600 to-purple-600", level: 88, category: "Design" },
  { name: "Photoshop", icon: Palette, color: "from-blue-500 to-cyan-500", level: 85, category: "Design" },
  { name: "Illustrator", icon: Palette, color: "from-orange-400 to-red-500", level: 83, category: "Design" },
  { name: "Canva", icon: Palette, color: "from-purple-400 to-pink-400", level: 95, category: "Design" },
  { name: "DaVinci Resolve", icon: Video, color: "from-red-400 to-orange-500", level: 80, category: "Design" },
  { name: "Adobe XD", icon: Palette, color: "from-pink-400 to-purple-500", level: 87, category: "Design" },

  // Cloud & DevOps
  { name: "AWS", icon: Code, color: "from-orange-400 to-yellow-500", level: 86, category: "Cloud & DevOps" },
  { name: "Azure", icon: Code, color: "from-blue-400 to-blue-600", level: 82, category: "Cloud & DevOps" },
  { name: "Google Cloud", icon: Code, color: "from-red-400 to-yellow-400", level: 84, category: "Cloud & DevOps" },
  { name: "Linux", icon: Code, color: "from-gray-600 to-gray-800", level: 85, category: "Cloud & DevOps" },
  { name: "Git/GitHub", icon: Code, color: "from-gray-700 to-black", level: 92, category: "Cloud & DevOps" },

  // Other Tools
  { name: "VS Code", icon: Code, color: "from-blue-500 to-blue-700", level: 95, category: "Tools" },
  { name: "Arduino/IoT", icon: Code, color: "from-teal-400 to-cyan-500", level: 78, category: "Tools" },
  { name: "Blockchain", icon: Code, color: "from-yellow-400 to-orange-500", level: 75, category: "Tools" },
]

// ============================================
// PROJECTS SECTION - ADD YOUR PROJECTS
// ============================================
const PROJECTS = [
  {
    id: 1,
    title: "EcoTrack Sustainability App",
    category: "UI/UX + Development",
    description:
      "A comprehensive gamified sustainability tracking application that helps users reduce their carbon footprint through daily challenges, community features, and real-time impact visualization.",
    longDescription:
      "EcoTrack is a mobile application designed to make sustainability engaging and accessible. The app features a comprehensive tracking system for carbon footprint, water usage, and waste generation. Users can participate in daily eco-challenges, connect with like-minded individuals, and visualize their environmental impact through beautiful data visualizations.",
    image: "/placeholder.svg?height=400&width=600",
    tags: ["React Native", "UI/UX Design", "Gamification", "Sustainability", "Mobile App"],
    color: "from-green-400 to-emerald-500",
    demoLink: "#",
    githubLink: "#",
    featured: true,
    year: "2024",
    status: "Completed",
  },
  {
    id: 2,
    title: "NeuroUI Analytics Dashboard",
    category: "AI + Design",
    description:
      "An intelligent analytics dashboard with AI-powered insights and beautiful data visualizations for business metrics and predictive analytics.",
    longDescription:
      "NeuroUI combines artificial intelligence with intuitive design to create a powerful analytics platform. The dashboard features machine learning algorithms that provide predictive insights, automated report generation, and interactive data visualizations that help businesses make data-driven decisions.",
    image: "/placeholder.svg?height=400&width=600",
    tags: ["React", "AI/ML", "Data Visualization", "Dashboard", "TypeScript"],
    color: "from-blue-400 to-cyan-500",
    demoLink: "#",
    githubLink: "#",
    featured: true,
    year: "2024",
    status: "Completed",
  },
  // Add more projects here...
]

// ============================================
// EDUCATION SECTION - ADD YOUR EDUCATION
// ============================================
const EDUCATION = [
  {
    degree: "Bachelor of Technology (CSE in AI/ML Specialization)",
    institution: "Aravali College Of Engineering & Management",
    period: "Oct 2021 - June 2025",
    description:
      "Pursuing Bachelor's degree in Computer Science Engineering with specialization in Artificial Intelligence and Machine Learning. Focus on full-stack development, AI/ML applications, and software engineering principles.",
    gpa: "7.7/10 CGPA",
    relevant_courses: [
      "Data Structures & Algorithms",
      "Machine Learning",
      "Artificial Intelligence",
      "Database Management Systems",
      "Software Engineering",
      "Computer Networks",
      "Operating Systems",
      "Web Technologies",
      "Deep Learning",
      "Natural Language Processing",
    ],
    achievements: [
      "Cultural Head for 2 consecutive years",
      "Lead Designer - Created 50+ posters and promotional content",
      "7x Hackathon Winner",
      "Active participant in tech fests and coding competitions",
    ],
  },
  {
    degree: "Higher Secondary Education (Class 12th - CBSE)",
    institution: "D.A.V. Public School",
    period: "2020 - May 2021",
    description:
      "Science stream with Mathematics, Physics, Chemistry, and Computer Science. Strong foundation in analytical thinking and problem-solving.",
    gpa: "79%",
    relevant_courses: ["Mathematics", "Physics", "Chemistry", "Computer Science", "English"],
    achievements: [
      "Consistent academic performance",
      "Active participation in science exhibitions",
      "Member of computer science club",
    ],
  },
  {
    degree: "Secondary Education (Class 10th - CBSE)",
    institution: "D.A.V. Public School",
    period: "May 2018 - May 2019",
    description:
      "Comprehensive secondary education with focus on science and mathematics. Developed strong analytical and logical reasoning skills.",
    gpa: "87%",
    relevant_courses: ["Mathematics", "Science", "Social Science", "English", "Hindi"],
    achievements: [
      "School topper in Computer Science",
      "Participated in inter-school competitions",
      "Member of school tech club",
    ],
  },
]

// ============================================
// WORK EXPERIENCE - ADD YOUR WORK EXPERIENCE
// ============================================
const WORK_EXPERIENCE = [
  // Tech Experience
  {
    title: "Software Trainer",
    company: "Codefeast",
    period: "Jan 2025 - June 2025",
    description:
      "Delivering hands-on training to 100+ learners, including working professionals, on web development, AI/ML, and Python. Conducting interactive sessions for startups on emerging technologies and AI integration.",
    technologies: ["Python", "Web Development", "AI/ML", "Training & Development"],
    type: "Training",
    category: "tech",
  },
  {
    title: "Full-Stack Developer",
    company: "Zidio Development",
    period: "Jul 2024 - Oct 2024",
    description:
      "Developed and deployed scalable full-stack applications using React and Node.js. Integrated RESTful APIs and improved user experience across web platforms, contributing to multiple client projects.",
    technologies: ["React", "Node.js", "REST APIs", "JavaScript", "MongoDB"],
    type: "Full-time",
    category: "tech",
  },
  {
    title: "AI/ML Research Assistant",
    company: "College Research Lab",
    period: "Aug 2023 - Dec 2023",
    description:
      "Worked on machine learning projects focusing on natural language processing and computer vision. Developed AI models for text analysis and image recognition with 90%+ accuracy.",
    technologies: ["Python", "TensorFlow", "Scikit-learn", "OpenCV", "NLP"],
    type: "Research",
    category: "tech",
  },
  // Design Experience
  {
    title: "Video Editor & Graphic Designer",
    company: "Aspect Ratio",
    period: "July 2024 - Dec 2024",
    description:
      "Edited real estate and school promotional reels and social media posts. Collaborated with marketing team to create engaging content and worked on social media marketing strategies to increase brand value.",
    technologies: ["Adobe Premiere Pro", "After Effects", "Photoshop", "Social Media Marketing"],
    type: "Full-time",
    category: "design",
  },
  {
    title: "Video Editor",
    company: "Systemic Altruism",
    period: "June 2024 - July 2024",
    description:
      "Edited videos for social media, corporate, and promotional content. Collaborated with marketing team to create engaging visual content using Adobe Premiere Pro and After Effects.",
    technologies: ["Adobe Premiere Pro", "After Effects", "Video Production"],
    type: "Contract",
    category: "design",
  },
  {
    title: "Video Editing Intern",
    company: "Seth Consultancy Services",
    period: "June 2023 - Oct 2023",
    description:
      "Edited videos for various clients, including promotional content, social media clips, and educational videos. Managed multiple projects simultaneously, ensuring timely delivery of all content.",
    technologies: ["Adobe Premiere Pro", "DaVinci Resolve", "Canva", "Project Management"],
    type: "Internship",
    category: "design",
  },
]

// ============================================
// HACKATHONS - ADD YOUR HACKATHON WINS
// ============================================
const HACKATHONS = [
  {
    name: "Smart India Hackathon 2024",
    position: "Winner",
    project: "AI-Powered Healthcare Solution",
    description: "Developed an AI-based medical diagnosis system using machine learning and computer vision.",
    date: "Dec 2024",
    prize: "$15,000",
    team_size: 6,
  },
  {
    name: "HackTheNorth 2024",
    position: "1st Place",
    project: "EcoTrack Sustainability App",
    description: "Built a gamified sustainability tracking app that won first place among 200+ teams.",
    date: "Sept 2024",
    prize: "$10,000",
    team_size: 4,
  },
  {
    name: "Google Solution Challenge 2024",
    position: "Top 10 Finalist",
    project: "Rural Education Platform",
    description: "Created an AI-powered educational platform for rural students with offline capabilities.",
    date: "Aug 2024",
    prize: "$5,000",
    team_size: 4,
  },
  {
    name: "Microsoft Imagine Cup 2024",
    position: "Regional Winner",
    project: "Smart Agriculture IoT System",
    description: "Developed IoT-based crop monitoring system with AI predictions for farmers.",
    date: "July 2024",
    prize: "$8,000",
    team_size: 5,
  },
  {
    name: "AWS DeepRacer Challenge 2024",
    position: "2nd Place",
    project: "Autonomous Vehicle AI",
    description: "Built reinforcement learning model for autonomous navigation in complex environments.",
    date: "June 2024",
    prize: "$3,000",
    team_size: 3,
  },
  {
    name: "Flipkart Grid 5.0",
    position: "Winner",
    project: "E-commerce Recommendation Engine",
    description: "Developed advanced ML-based product recommendation system with 95% accuracy.",
    date: "May 2024",
    prize: "$12,000",
    team_size: 4,
  },
  {
    name: "TechFest IIT Bombay 2024",
    position: "1st Place",
    project: "Blockchain Voting System",
    description: "Created secure, transparent voting platform using blockchain and smart contracts.",
    date: "Apr 2024",
    prize: "$6,000",
    team_size: 4,
  },
  {
    name: "HackIndia 2023",
    position: "Winner",
    project: "Mental Health AI Chatbot",
    description: "Built AI-powered mental health support chatbot with NLP and sentiment analysis.",
    date: "Nov 2023",
    prize: "$4,000",
    team_size: 3,
  },
]

// ============================================
// ACHIEVEMENTS - ADD YOUR ACHIEVEMENTS
// ============================================
const ACHIEVEMENTS = [
  {
    year: "2024",
    title: "7x Hackathon Winner",
    description: "Recognized for innovation and teamwork across multiple national and international hackathons",
    icon: "🏆",
    category: "Competition",
  },
  {
    year: "2023-2024",
    title: "Cultural Head (2 Years)",
    description: "Led large-scale college events and tech fests, managing teams of 50+ students",
    icon: "👥",
    category: "Leadership",
  },
  {
    year: "2024",
    title: "Lead Designer",
    description: "Created 50+ posters, promotional videos, and branding content for college events",
    icon: "🎨",
    category: "Design",
  },
  {
    year: "2024",
    title: "AWS Certified",
    description: "AWS Generative AI & DevOps Learning Path certification",
    icon: "☁️",
    category: "Certification",
  },
  {
    year: "2024",
    title: "Google Cloud Certified",
    description: "Google Cloud Fundamentals certification with hands-on project experience",
    icon: "🌐",
    category: "Certification",
  },
  {
    year: "2024",
    title: "Microsoft AI Certified",
    description: "Microsoft Certified Generative AI Career Essentials in association with LinkedIn",
    icon: "🤖",
    category: "Certification",
  },
]

// ============================================
// FUN FACTS - ADD YOUR PERSONAL TOUCHES
// ============================================
const FUN_FACTS = [
  { icon: Coffee, text: "Powered by chai and creativity" },
  { icon: Music, text: "Designs better with lo-fi beats" },
  { icon: Camera, text: "Weekend photographer & storyteller" },
]

// ============================================
// ANIMATED BACKGROUND COMPONENT
// ============================================
const AnimatedBackground = () => {
  const [particles, setParticles] = useState([])

  useEffect(() => {
    const newParticles = []
    for (let i = 0; i < 50; i++) {
      newParticles.push({
        id: i,
        x: Math.random() * 100,
        y: Math.random() * 100,
        size: Math.random() * 4 + 1,
        duration: Math.random() * 20 + 10,
      })
    }
    setParticles(newParticles)
  }, [])

  return (
    <div className="fixed inset-0 overflow-hidden pointer-events-none z-0">
      {particles.map((particle) => (
        <motion.div
          key={particle.id}
          className="absolute rounded-full bg-gradient-to-r from-cyan-400 to-purple-500 opacity-20"
          style={{
            left: `${particle.x}%`,
            top: `${particle.y}%`,
            width: `${particle.size}px`,
            height: `${particle.size}px`,
          }}
          animate={{
            y: [-20, -100],
            opacity: [0, 0.6, 0],
          }}
          transition={{
            duration: particle.duration,
            repeat: Number.POSITIVE_INFINITY,
            ease: "linear",
          }}
        />
      ))}

      {/* Geometric shapes */}
      <div className="absolute inset-0">
        {[...Array(8)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute border border-cyan-400/20 rounded-lg"
            style={{
              left: `${Math.random() * 90}%`,
              top: `${Math.random() * 90}%`,
              width: `${Math.random() * 60 + 40}px`,
              height: `${Math.random() * 60 + 40}px`,
            }}
            animate={{
              rotate: [0, 360],
              scale: [1, 1.2, 1],
            }}
            transition={{
              duration: Math.random() * 10 + 10,
              repeat: Number.POSITIVE_INFINITY,
              ease: "linear",
            }}
          />
        ))}
      </div>
    </div>
  )
}

// ============================================
// MAIN PORTFOLIO COMPONENT
// ============================================
export default function Portfolio() {
  const [isDarkMode, setIsDarkMode] = useState(false)
  const [selectedProject, setSelectedProject] = useState(null)
  const [activeFilter, setActiveFilter] = useState("all")
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [currentSection, setCurrentSection] = useState("hero")
  const [activeSkillFilter, setActiveSkillFilter] = useState("all")

  // Filter projects based on active filter
  const filteredProjects = PROJECTS.filter((project) => {
    if (activeFilter === "all") return true
    if (activeFilter === "featured") return project.featured
    return project.category.toLowerCase().includes(activeFilter.toLowerCase())
  })

  const projectCategories = ["all", "featured", "ui/ux", "development", "video", "design"]

  // Scroll to section function
  const scrollToSection = (sectionId) => {
    const element = document.getElementById(sectionId)
    if (element) {
      element.scrollIntoView({ behavior: "smooth" })
      setIsMenuOpen(false)
    }
  }

  // Navigation items
  const navItems = [
    { label: "Home", id: "hero" },
    { label: "About", id: "about" },
    { label: "Skills", id: "skills" },
    { label: "Projects", id: "projects" },
    { label: "Education", id: "education" },
    { label: "Experience", id: "experience" },
    { label: "Hackathons", id: "hackathons" },
    { label: "Contact", id: "contact" },
  ]

  const skillCategories = [
    "all",
    "programming",
    "web development",
    "database",
    "ai/ml",
    "design",
    "cloud & devops",
    "tools",
  ]

  const filteredSkills = SKILLS.filter((skill) => {
    if (activeSkillFilter === "all") return true
    return skill.category.toLowerCase() === activeSkillFilter
  })

  return (
    <div
      className={`min-h-screen transition-all duration-500 ${isDarkMode ? "dark bg-slate-900" : "bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900"}`}
    >
      {/* Animated Background */}
      <AnimatedBackground />

      {/* Navigation */}
      <nav className="fixed top-0 left-0 right-0 z-50 backdrop-blur-xl bg-black/20 border-b border-cyan-500/20">
        <div className="max-w-7xl mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            {/* Logo/Brand - Removed text, just icon */}
            <motion.div initial={{ opacity: 0, x: -20 }} animate={{ opacity: 1, x: 0 }} className="flex items-center">
              <div className="w-10 h-10 rounded-lg bg-gradient-to-br from-cyan-400 to-purple-500 flex items-center justify-center">
                <Zap className="h-6 w-6 text-white" />
              </div>
            </motion.div>

            {/* Desktop Navigation */}
            <div className="hidden lg:flex items-center space-x-8">
              {navItems.map((item) => (
                <motion.button
                  key={item.id}
                  className="text-gray-300 hover:text-cyan-400 transition-all duration-300 font-medium relative group"
                  onClick={() => scrollToSection(item.id)}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  {item.label}
                  <span className="absolute inset-x-0 bottom-0 h-0.5 bg-gradient-to-r from-cyan-400 to-purple-500 scale-x-0 group-hover:scale-x-100 transition-transform duration-300" />
                </motion.button>
              ))}
            </div>

            {/* Theme Toggle & Mobile Menu */}
            <div className="flex items-center space-x-3">
              <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => setIsDarkMode(!isDarkMode)}
                  className="rounded-full bg-white/10 backdrop-blur-sm hover:bg-white/20 border border-cyan-500/30"
                >
                  {isDarkMode ? <Sun className="h-5 w-5 text-cyan-400" /> : <Moon className="h-5 w-5 text-cyan-400" />}
                </Button>
              </motion.div>

              <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                <Button
                  className="bg-gradient-to-r from-cyan-500 to-purple-600 hover:from-cyan-600 hover:to-purple-700 text-white rounded-full px-6 hidden sm:flex border-0 shadow-lg shadow-cyan-500/25"
                  onClick={() => window.open(PERSONAL_INFO.resumeLink, "_blank")}
                >
                  <Download className="mr-2 h-4 w-4" />
                  Resume
                </Button>
              </motion.div>

              {/* Mobile Menu Toggle */}
              <Button
                variant="ghost"
                size="icon"
                className="lg:hidden rounded-full bg-white/10 backdrop-blur-sm hover:bg-white/20 border border-cyan-500/30"
                onClick={() => setIsMenuOpen(!isMenuOpen)}
              >
                {isMenuOpen ? <X className="h-5 w-5 text-cyan-400" /> : <Menu className="h-5 w-5 text-cyan-400" />}
              </Button>
            </div>
          </div>

          {/* Mobile Navigation */}
          <AnimatePresence>
            {isMenuOpen && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: "auto" }}
                exit={{ opacity: 0, height: 0 }}
                className="lg:hidden mt-4 pb-4 border-t border-cyan-500/20"
              >
                <div className="flex flex-col space-y-3 pt-4">
                  {navItems.map((item) => (
                    <motion.button
                      key={item.id}
                      className="text-left text-gray-300 hover:text-cyan-400 transition-colors py-2 font-medium"
                      onClick={() => scrollToSection(item.id)}
                      whileHover={{ x: 10 }}
                    >
                      {item.label}
                    </motion.button>
                  ))}
                  <Button
                    className="bg-gradient-to-r from-cyan-500 to-purple-600 hover:from-cyan-600 hover:to-purple-700 text-white rounded-full mt-4 border-0"
                    onClick={() => window.open(PERSONAL_INFO.resumeLink, "_blank")}
                  >
                    <Download className="mr-2 h-4 w-4" />
                    Download Resume
                  </Button>
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </nav>

      {/* Hero Section */}
      <section id="hero" className="min-h-screen flex items-center justify-center pt-20 px-6 relative z-10">
        <div className="max-w-6xl mx-auto text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="mb-8"
          >
            {/* Profile Image */}
            <motion.div
              className="w-40 h-40 mx-auto mb-8 rounded-full p-1 bg-gradient-to-br from-cyan-400 via-purple-500 to-pink-500"
              whileHover={{ scale: 1.05 }}
              transition={{ duration: 0.3 }}
            >
              <div className="w-full h-full rounded-full bg-slate-900 p-2">
                <img
                  src={PERSONAL_INFO.profileImage || "/placeholder.svg"}
                  alt="Profile"
                  className="w-full h-full rounded-full object-cover border-2 border-cyan-400/30"
                />
              </div>
            </motion.div>

            <motion.h1
              className="text-5xl md:text-7xl font-bold mb-6"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
            >
              <span className="bg-gradient-to-r from-cyan-400 via-purple-400 to-pink-400 bg-clip-text text-transparent">
                Hi, I'm {PERSONAL_INFO.name}
              </span>
            </motion.h1>

            <motion.p
              className="text-xl md:text-2xl text-gray-300 mb-8 max-w-3xl mx-auto"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.4 }}
            >
              Designing with purpose, building with impact.
              <br />
              <span className="text-transparent bg-gradient-to-r from-cyan-400 to-purple-400 bg-clip-text font-semibold">
                {PERSONAL_INFO.title}
              </span>
            </motion.p>
          </motion.div>

          {/* CTA Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-12">
            <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
              <Button
                size="lg"
                className="bg-gradient-to-r from-cyan-500 to-purple-600 hover:from-cyan-600 hover:to-purple-700 text-white rounded-full px-8 py-3 border-0 shadow-lg shadow-cyan-500/25"
                onClick={() => scrollToSection("projects")}
              >
                <Sparkles className="mr-2 h-5 w-5" />
                View My Work
              </Button>
            </motion.div>

            <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
              <Button
                variant="outline"
                size="lg"
                className="border-cyan-400 text-cyan-400 hover:bg-cyan-400/10 backdrop-blur-sm rounded-full px-8 py-3"
                onClick={() => scrollToSection("contact")}
              >
                Let's Connect
              </Button>
            </motion.div>
          </div>

          {/* Resume Download Options */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.7 }}
            className="flex flex-col sm:flex-row gap-3 justify-center items-center mb-8"
          >
            <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
              <Button
                variant="outline"
                className="border-purple-400 text-purple-400 hover:bg-purple-400/10 backdrop-blur-sm rounded-full px-6 py-2"
                onClick={() => window.open(PERSONAL_INFO.developerResumeLink, "_blank")}
              >
                <Download className="mr-2 h-4 w-4" />
                Developer Resume
              </Button>
            </motion.div>

            <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
              <Button
                variant="outline"
                className="border-pink-400 text-pink-400 hover:bg-pink-400/10 backdrop-blur-sm rounded-full px-6 py-2"
                onClick={() => window.open(PERSONAL_INFO.designerResumeLink, "_blank")}
              >
                <Download className="mr-2 h-4 w-4" />
                Designer Resume
              </Button>
            </motion.div>
          </motion.div>

          {/* Social Links */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.8 }}
            className="flex justify-center gap-4"
          >
            {Object.entries(SOCIAL_LINKS).map(([platform, url]) => {
              const icons = {
                github: Github,
                gitlab: GitBranch,
                linkedin: Linkedin,
                instagram: Instagram,
                email: Mail,
              }
              const Icon = icons[platform]
              return (
                <motion.button
                  key={platform}
                  whileHover={{ scale: 1.1, y: -2 }}
                  whileTap={{ scale: 0.95 }}
                  className="p-3 rounded-full bg-white/10 backdrop-blur-sm border border-cyan-500/30 hover:border-cyan-400/50 hover:bg-white/20 transition-all duration-300"
                  onClick={() => window.open(url, "_blank")}
                >
                  <Icon className="h-5 w-5 text-gray-300 hover:text-cyan-400 transition-colors" />
                </motion.button>
              )
            })}
          </motion.div>

          {/* Scroll Indicator */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 1, delay: 1 }}
            className="absolute bottom-8 left-1/2 transform -translate-x-1/2"
          >
            <motion.div
              animate={{ y: [0, 10, 0] }}
              transition={{ duration: 2, repeat: Number.POSITIVE_INFINITY }}
              className="text-cyan-400/60"
            >
              <ChevronDown className="h-6 w-6" />
            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* About Me Section */}
      <section id="about" className="py-20 px-6 relative z-10">
        <div className="max-w-4xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl md:text-5xl font-bold mb-6 bg-gradient-to-r from-cyan-400 to-purple-500 bg-clip-text text-transparent">
              About Me
            </h2>
          </motion.div>

          <div className="grid md:grid-cols-2 gap-12 items-center">
            <motion.div
              initial={{ opacity: 0, x: -30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
            >
              <div className="w-64 h-64 mx-auto rounded-3xl bg-gradient-to-br from-cyan-400 via-purple-500 to-pink-500 p-1">
                <div className="w-full h-full rounded-3xl bg-slate-900 flex items-center justify-center">
                  <span className="text-8xl">👩‍🎨</span>
                </div>
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: 30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              className="space-y-6"
            >
              <p className="text-lg text-gray-300 leading-relaxed">{PERSONAL_INFO.bio}</p>

              <p className="text-lg text-gray-300 leading-relaxed">
                When I'm not crafting user experiences or building applications, you'll find me leading community
                initiatives, winning hackathons, or experimenting with the latest design trends and technologies.
              </p>

              <div className="space-y-4">
                <h3 className="text-xl font-semibold text-white mb-4">3 Fun Facts About Me:</h3>
                {FUN_FACTS.map((fact, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: index * 0.1 }}
                    whileHover={{ x: 10, scale: 1.02 }}
                    className="flex items-center gap-3 p-4 rounded-xl bg-white/5 backdrop-blur-sm border border-cyan-500/20 hover:border-cyan-400/40 transition-all duration-300 cursor-pointer"
                  >
                    <fact.icon className="h-6 w-6 text-cyan-400" />
                    <span className="text-gray-300">{fact.text}</span>
                  </motion.div>
                ))}
              </div>

              <div className="flex gap-3 pt-4">
                <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                  <Button
                    className="bg-gradient-to-r from-cyan-500 to-purple-600 hover:from-cyan-600 hover:to-purple-700 text-white border-0"
                    onClick={() => scrollToSection("contact")}
                  >
                    <Mail className="mr-2 h-4 w-4" />
                    Get In Touch
                  </Button>
                </motion.div>

                <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                  <Button
                    variant="outline"
                    className="border-cyan-400 text-cyan-400 hover:bg-cyan-400/10"
                    onClick={() => window.open(PERSONAL_INFO.resumeLink, "_blank")}
                  >
                    <Download className="mr-2 h-4 w-4" />
                    Resume
                  </Button>
                </motion.div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Skills Section */}
      <section id="skills" className="py-20 px-6 relative z-10">
        <div className="max-w-6xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl md:text-5xl font-bold mb-6 bg-gradient-to-r from-cyan-400 to-purple-500 bg-clip-text text-transparent">
              My Creative Toolkit
            </h2>
            <p className="text-xl text-gray-300 max-w-2xl mx-auto">
              Each skill is a carefully crafted tool in my digital studio, ready to bring ideas to life.
            </p>
          </motion.div>

          {/* Skills Filter */}
          <div className="flex flex-wrap justify-center gap-2 mb-8">
            {skillCategories.map((category) => (
              <motion.div key={category} whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                <Button
                  variant={activeSkillFilter === category ? "default" : "outline"}
                  size="sm"
                  onClick={() => setActiveSkillFilter(category)}
                  className={`capitalize ${
                    activeSkillFilter === category
                      ? "bg-gradient-to-r from-cyan-500 to-purple-600 text-white border-0"
                      : "border-cyan-400 text-cyan-400 hover:bg-cyan-400/10"
                  }`}
                >
                  {category}
                </Button>
              </motion.div>
            ))}
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredSkills.map((skill, index) => (
              <motion.div
                key={skill.name}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                whileHover={{ y: -8, scale: 1.02 }}
                className="group"
              >
                <Card className="h-full bg-white/5 backdrop-blur-sm border border-cyan-500/20 hover:border-cyan-400/40 shadow-lg hover:shadow-xl hover:shadow-cyan-500/10 transition-all duration-300">
                  <CardContent className="p-6">
                    <motion.div
                      className={`w-16 h-16 rounded-2xl bg-gradient-to-br ${skill.color} flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300`}
                      whileHover={{ rotate: [0, -10, 10, 0] }}
                      transition={{ duration: 0.5 }}
                    >
                      <skill.icon className="h-8 w-8 text-white" />
                    </motion.div>
                    <h3 className="text-xl font-semibold mb-2 text-white group-hover:text-cyan-400 transition-colors">
                      {skill.name}
                    </h3>
                    <div className="w-full bg-slate-700 rounded-full h-2 mb-2 overflow-hidden">
                      <motion.div
                        initial={{ width: 0 }}
                        whileInView={{ width: `${skill.level}%` }}
                        transition={{ duration: 1, delay: 0.5 }}
                        className={`h-2 rounded-full bg-gradient-to-r ${skill.color}`}
                      />
                    </div>
                    <span className="text-sm text-gray-400">{skill.level}% Proficiency</span>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Projects Section */}
      <section id="projects" className="py-20 px-6 relative z-10">
        <div className="max-w-6xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl md:text-5xl font-bold mb-6 bg-gradient-to-r from-cyan-400 to-purple-500 bg-clip-text text-transparent">
              Featured Projects
            </h2>
            <p className="text-xl text-gray-300 max-w-2xl mx-auto mb-8">
              A showcase of my favorite creations, where design meets functionality and innovation.
            </p>

            {/* Project Filter */}
            <div className="flex flex-wrap justify-center gap-2 mb-8">
              {projectCategories.map((category) => (
                <motion.div key={category} whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                  <Button
                    variant={activeFilter === category ? "default" : "outline"}
                    size="sm"
                    onClick={() => setActiveFilter(category)}
                    className={`capitalize ${
                      activeFilter === category
                        ? "bg-gradient-to-r from-cyan-500 to-purple-600 text-white border-0"
                        : "border-cyan-400 text-cyan-400 hover:bg-cyan-400/10"
                    }`}
                  >
                    {category}
                  </Button>
                </motion.div>
              ))}
            </div>
          </motion.div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <AnimatePresence>
              {filteredProjects.map((project, index) => (
                <motion.div
                  key={project.id}
                  initial={{ opacity: 0, y: 30 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: 30 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  whileHover={{ y: -8, scale: 1.02 }}
                  className="group"
                >
                  <Card className="h-full bg-white/5 backdrop-blur-sm border border-cyan-500/20 hover:border-cyan-400/40 shadow-lg hover:shadow-xl hover:shadow-cyan-500/10 transition-all duration-500 overflow-hidden">
                    <div className="relative overflow-hidden">
                      <motion.img
                        src={project.image || "/placeholder.svg"}
                        alt={project.title}
                        className="w-full h-48 object-cover group-hover:scale-110 transition-transform duration-500"
                        whileHover={{ scale: 1.1 }}
                      />
                      <div
                        className={`absolute top-4 left-4 px-3 py-1 rounded-full text-white text-sm font-medium bg-gradient-to-r ${project.color} backdrop-blur-sm`}
                      >
                        {project.category}
                      </div>
                      {project.featured && (
                        <div className="absolute top-4 right-4 px-3 py-1 rounded-full bg-yellow-500 text-white text-sm font-medium backdrop-blur-sm">
                          <Star className="inline h-3 w-3 mr-1" />
                          Featured
                        </div>
                      )}
                    </div>

                    <CardContent className="p-6">
                      <div className="flex justify-between items-start mb-2">
                        <h3 className="text-2xl font-bold text-white group-hover:text-cyan-400 transition-colors">
                          {project.title}
                        </h3>
                        <span className="text-sm text-gray-400">{project.year}</span>
                      </div>

                      <p className="text-gray-300 mb-4 leading-relaxed">{project.description}</p>

                      <div className="flex flex-wrap gap-2 mb-6">
                        {project.tags.map((tag) => (
                          <Badge
                            key={tag}
                            variant="secondary"
                            className="bg-cyan-500/10 text-cyan-400 border border-cyan-500/20 hover:bg-cyan-500/20 transition-colors"
                          >
                            {tag}
                          </Badge>
                        ))}
                      </div>

                      <div className="flex gap-3">
                        <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                          <Button
                            size="sm"
                            className="bg-gradient-to-r from-cyan-500 to-purple-600 hover:from-cyan-600 hover:to-purple-700 text-white border-0"
                            onClick={() => window.open(project.demoLink, "_blank")}
                          >
                            <ExternalLink className="mr-2 h-4 w-4" />
                            Live Demo
                          </Button>
                        </motion.div>

                        <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                          <Button
                            variant="outline"
                            size="sm"
                            className="border-cyan-400 text-cyan-400 hover:bg-cyan-400/10"
                            onClick={() => window.open(project.githubLink, "_blank")}
                          >
                            <Github className="mr-2 h-4 w-4" />
                            Code
                          </Button>
                        </motion.div>

                        <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                          <Button
                            variant="outline"
                            size="sm"
                            className="border-cyan-400 text-cyan-400 hover:bg-cyan-400/10"
                            onClick={() => setSelectedProject(project)}
                          >
                            <Eye className="mr-2 h-4 w-4" />
                            Details
                          </Button>
                        </motion.div>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </AnimatePresence>
          </div>

          {filteredProjects.length === 0 && (
            <div className="text-center py-12">
              <p className="text-gray-400">No projects found for the selected filter.</p>
            </div>
          )}
        </div>
      </section>

      {/* Education Section */}
      <section id="education" className="py-20 px-6 relative z-10">
        <div className="max-w-6xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl md:text-5xl font-bold mb-6 bg-gradient-to-r from-cyan-400 to-purple-500 bg-clip-text text-transparent">
              Education
            </h2>
            <p className="text-xl text-gray-300">My academic journey and qualifications.</p>
          </motion.div>

          <div className="space-y-8">
            {EDUCATION.map((edu, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, x: -30 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                whileHover={{ scale: 1.02, x: 10 }}
              >
                <Card className="bg-white/5 backdrop-blur-sm border border-cyan-500/20 hover:border-cyan-400/40 shadow-lg hover:shadow-xl hover:shadow-cyan-500/10 transition-all duration-300">
                  <CardContent className="p-6">
                    <div className="flex items-start gap-4">
                      <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-cyan-400 to-purple-500 flex items-center justify-center flex-shrink-0">
                        <GraduationCap className="h-6 w-6 text-white" />
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-2">
                          <h3 className="text-xl font-bold text-white">{edu.degree}</h3>
                          <Badge className="bg-cyan-500/20 text-cyan-400 border border-cyan-500/30">{edu.gpa}</Badge>
                        </div>
                        <p className="text-cyan-400 font-medium mb-2">{edu.institution}</p>
                        <p className="text-gray-300 mb-3">{edu.description}</p>
                        <div className="flex items-center gap-4">
                          <span className="text-sm text-cyan-400 font-medium flex items-center gap-1">
                            <Calendar className="h-4 w-4" />
                            {edu.period}
                          </span>
                        </div>
                        {edu.relevant_courses && (
                          <div className="mt-3">
                            <p className="text-sm text-gray-400 mb-2">Key Courses:</p>
                            <div className="flex flex-wrap gap-2">
                              {edu.relevant_courses.map((course, idx) => (
                                <Badge
                                  key={idx}
                                  variant="outline"
                                  className="text-xs border-purple-500/30 text-purple-400"
                                >
                                  {course}
                                </Badge>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Work Experience Section */}
      <section id="experience" className="py-20 px-6 relative z-10">
        <div className="max-w-6xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl md:text-5xl font-bold mb-6 bg-gradient-to-r from-cyan-400 to-purple-500 bg-clip-text text-transparent">
              Work Experience
            </h2>
            <p className="text-xl text-gray-300">My professional journey and contributions.</p>
          </motion.div>

          <div className="relative">
            <div className="absolute left-8 top-0 bottom-0 w-0.5 bg-gradient-to-b from-cyan-400 to-purple-600"></div>

            {WORK_EXPERIENCE.map((exp, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, x: -30 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                whileHover={{ scale: 1.02, x: 10 }}
                className="relative mb-8"
              >
                <div className="absolute left-6 w-4 h-4 bg-gradient-to-br from-cyan-400 to-purple-600 rounded-full border-4 border-slate-900"></div>

                <Card className="ml-16 bg-white/5 backdrop-blur-sm border border-cyan-500/20 hover:border-cyan-400/40 shadow-lg hover:shadow-xl hover:shadow-cyan-500/10 transition-all duration-300">
                  <CardContent className="p-6">
                    <div className="flex items-center gap-2 mb-2">
                      <Briefcase className="h-5 w-5 text-cyan-400" />
                      <Badge className="bg-cyan-500/20 text-cyan-400 border border-cyan-500/30">{exp.type}</Badge>
                      <span className="text-sm font-medium text-purple-400 ml-auto">{exp.period}</span>
                    </div>
                    <h3 className="text-xl font-bold mb-1 text-white">{exp.title}</h3>
                    <p className="text-cyan-400 font-medium mb-2">{exp.company}</p>
                    <p className="text-gray-300 mb-3">{exp.description}</p>
                    {exp.technologies && (
                      <div className="flex flex-wrap gap-2">
                        {exp.technologies.map((tech, idx) => (
                          <Badge key={idx} variant="outline" className="text-xs border-purple-500/30 text-purple-400">
                            {tech}
                          </Badge>
                        ))}
                      </div>
                    )}
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Hackathons Section */}
      <section id="hackathons" className="py-20 px-6 relative z-10">
        <div className="max-w-6xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl md:text-5xl font-bold mb-6 bg-gradient-to-r from-cyan-400 to-purple-500 bg-clip-text text-transparent">
              Hackathons & Competitions
            </h2>
            <p className="text-xl text-gray-300">Competitive programming and innovation challenges.</p>
          </motion.div>

          <div className="grid md:grid-cols-2 gap-8">
            {HACKATHONS.map((hackathon, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                whileHover={{ y: -8, scale: 1.02 }}
                className="group"
              >
                <Card className="h-full bg-white/5 backdrop-blur-sm border border-cyan-500/20 hover:border-cyan-400/40 shadow-lg hover:shadow-xl hover:shadow-cyan-500/10 transition-all duration-300">
                  <CardContent className="p-6">
                    <div className="flex items-start gap-4">
                      <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-yellow-400 to-orange-500 flex items-center justify-center flex-shrink-0">
                        <Trophy className="h-6 w-6 text-white" />
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-2">
                          <h3 className="text-xl font-bold text-white group-hover:text-cyan-400 transition-colors">
                            {hackathon.name}
                          </h3>
                          <Badge className="bg-yellow-500/20 text-yellow-400 border border-yellow-500/30">
                            {hackathon.position}
                          </Badge>
                        </div>
                        <p className="text-cyan-400 font-medium mb-2">{hackathon.project}</p>
                        <p className="text-gray-300 mb-3">{hackathon.description}</p>
                        <div className="flex items-center gap-4 text-sm text-gray-400">
                          <span className="flex items-center gap-1">
                            <Calendar className="h-4 w-4" />
                            {hackathon.date}
                          </span>
                          <span className="flex items-center gap-1">
                            <Users className="h-4 w-4" />
                            Team of {hackathon.team_size}
                          </span>
                          <span className="flex items-center gap-1">
                            <Target className="h-4 w-4" />
                            {hackathon.prize}
                          </span>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>

          {/* Achievements */}
          <div className="mt-16">
            <h3 className="text-2xl font-bold mb-8 text-white text-center">Other Achievements</h3>
            <div className="grid md:grid-cols-3 gap-6">
              {ACHIEVEMENTS.map((achievement, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  whileHover={{ scale: 1.05 }}
                >
                  <Card className="bg-white/5 backdrop-blur-sm border border-cyan-500/20 hover:border-cyan-400/40 shadow-lg hover:shadow-xl hover:shadow-cyan-500/10 transition-all duration-300">
                    <CardContent className="p-6 text-center">
                      <div className="text-4xl mb-3">{achievement.icon}</div>
                      <h3 className="text-lg font-bold text-white mb-2">{achievement.title}</h3>
                      <p className="text-gray-300 text-sm mb-3">{achievement.description}</p>
                      <div className="flex items-center justify-center gap-2">
                        <Badge variant="outline" className="text-xs border-cyan-500/30 text-cyan-400">
                          {achievement.category}
                        </Badge>
                        <span className="text-xs text-purple-400 font-medium">{achievement.year}</span>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section id="contact" className="py-20 px-6 relative z-10">
        <div className="max-w-4xl mx-auto text-center">
          <motion.div initial={{ opacity: 0, y: 30 }} whileInView={{ opacity: 1, y: 0 }} transition={{ duration: 0.8 }}>
            <h2 className="text-4xl md:text-5xl font-bold mb-6 bg-gradient-to-r from-cyan-400 to-purple-500 bg-clip-text text-transparent">
              Let's Create Together
            </h2>
            <p className="text-xl text-gray-300 mb-12 max-w-2xl mx-auto">
              Ready to bring your ideas to life? I'm always excited to collaborate on meaningful projects that make a
              difference. Let's connect and create something amazing!
            </p>

            <Card className="max-w-2xl mx-auto bg-white/5 backdrop-blur-sm border border-cyan-500/20 shadow-xl">
              <CardContent className="p-8">
                {/* Contact Info */}
                <div className="grid md:grid-cols-2 gap-6 mb-8">
                  <motion.div
                    className="flex items-center gap-3 p-4 rounded-lg bg-cyan-500/10 border border-cyan-500/20"
                    whileHover={{ scale: 1.05 }}
                  >
                    <Mail className="h-5 w-5 text-cyan-400" />
                    <div className="text-left">
                      <p className="text-sm text-gray-400">Email</p>
                      <p className="font-medium text-white">{PERSONAL_INFO.email}</p>
                    </div>
                  </motion.div>
                  <motion.div
                    className="flex items-center gap-3 p-4 rounded-lg bg-purple-500/10 border border-purple-500/20"
                    whileHover={{ scale: 1.05 }}
                  >
                    <MapPin className="h-5 w-5 text-purple-400" />
                    <div className="text-left">
                      <p className="text-sm text-gray-400">Location</p>
                      <p className="font-medium text-white">{PERSONAL_INFO.location}</p>
                    </div>
                  </motion.div>
                </div>

                <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-8">
                  <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                    <Button
                      size="lg"
                      className="bg-gradient-to-r from-cyan-500 to-purple-600 hover:from-cyan-600 hover:to-purple-700 text-white rounded-full px-8 border-0"
                      onClick={() => window.open(SOCIAL_LINKS.email, "_blank")}
                    >
                      <Mail className="mr-2 h-5 w-5" />
                      Send Email
                    </Button>
                  </motion.div>

                  <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                    <Button
                      variant="outline"
                      size="lg"
                      className="border-cyan-400 text-cyan-400 hover:bg-cyan-400/10 rounded-full px-8"
                      onClick={() => window.open(PERSONAL_INFO.resumeLink, "_blank")}
                    >
                      <Download className="mr-2 h-5 w-5" />
                      Download Resume
                    </Button>
                  </motion.div>
                </div>

                <div className="flex justify-center gap-4">
                  {Object.entries(SOCIAL_LINKS).map(([platform, url]) => {
                    const icons = {
                      github: Github,
                      gitlab: GitBranch,
                      linkedin: Linkedin,
                      instagram: Instagram,
                      email: Mail,
                    }
                    const Icon = icons[platform]
                    const colors = {
                      github: "hover:text-white",
                      gitlab: "hover:text-orange-400",
                      linkedin: "hover:text-blue-400",
                      instagram: "hover:text-pink-400",
                      email: "hover:text-cyan-400",
                    }
                    return (
                      <motion.button
                        key={platform}
                        whileHover={{ scale: 1.1, y: -2 }}
                        whileTap={{ scale: 0.95 }}
                        className={`p-4 rounded-full bg-white/5 backdrop-blur-sm border border-cyan-500/20 hover:border-cyan-400/40 text-gray-300 ${colors[platform]} transition-all duration-300`}
                        onClick={() => window.open(url, "_blank")}
                      >
                        <Icon className="h-6 w-6" />
                      </motion.button>
                    )
                  })}
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </section>

      {/* Footer */}
      <footer className="py-8 px-6 text-center text-gray-400 border-t border-cyan-500/20 relative z-10">
        <div className="max-w-6xl mx-auto">
          <p className="flex items-center justify-center gap-2 mb-4">
            Made with <Heart className="h-4 w-4 text-cyan-400" /> by {PERSONAL_INFO.name} • 2024
          </p>
          <p className="text-sm">Built with Next.js, Tailwind CSS, and Framer Motion</p>
        </div>
      </footer>

      {/* Project Detail Modal */}
      <AnimatePresence>
        {selectedProject && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 z-50 bg-black/80 backdrop-blur-sm flex items-center justify-center p-4"
            onClick={() => setSelectedProject(null)}
          >
            <motion.div
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.8, opacity: 0 }}
              onClick={(e) => e.stopPropagation()}
              className="max-w-4xl w-full max-h-[90vh] overflow-y-auto"
            >
              <Card className="bg-slate-900 border border-cyan-500/30 shadow-2xl">
                <CardContent className="p-0">
                  {/* Project Image */}
                  <div className="relative h-64 md:h-80">
                    <img
                      src={selectedProject.image || "/placeholder.svg"}
                      alt={selectedProject.title}
                      className="w-full h-full object-cover"
                    />
                    <div className="absolute top-4 right-4">
                      <Button
                        variant="outline"
                        size="icon"
                        onClick={() => setSelectedProject(null)}
                        className="bg-black/50 backdrop-blur-sm border-cyan-500/30 hover:bg-black/70"
                      >
                        <X className="h-4 w-4 text-white" />
                      </Button>
                    </div>
                    <div
                      className={`absolute bottom-4 left-4 px-3 py-1 rounded-full text-white text-sm font-medium bg-gradient-to-r ${selectedProject.color} backdrop-blur-sm`}
                    >
                      {selectedProject.category}
                    </div>
                  </div>

                  {/* Project Details */}
                  <div className="p-8">
                    <div className="flex justify-between items-start mb-4">
                      <h2 className="text-3xl font-bold text-white">{selectedProject.title}</h2>
                      <div className="flex items-center gap-2">
                        <Badge variant="outline" className="border-cyan-500/30 text-cyan-400">
                          {selectedProject.year}
                        </Badge>
                        <Badge variant="outline" className="border-green-500/30 text-green-400">
                          {selectedProject.status}
                        </Badge>
                      </div>
                    </div>

                    <p className="text-lg text-gray-300 mb-6 leading-relaxed">{selectedProject.longDescription}</p>

                    <div className="mb-6">
                      <h3 className="text-lg font-semibold mb-3 text-white">Technologies Used</h3>
                      <div className="flex flex-wrap gap-2">
                        {selectedProject.tags.map((tag) => (
                          <Badge
                            key={tag}
                            variant="secondary"
                            className="bg-cyan-500/10 text-cyan-400 border border-cyan-500/20"
                          >
                            {tag}
                          </Badge>
                        ))}
                      </div>
                    </div>

                    <div className="flex gap-4">
                      <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                        <Button
                          className="bg-gradient-to-r from-cyan-500 to-purple-600 hover:from-cyan-600 hover:to-purple-700 text-white border-0"
                          onClick={() => window.open(selectedProject.demoLink, "_blank")}
                        >
                          <ExternalLink className="mr-2 h-4 w-4" />
                          Live Demo
                        </Button>
                      </motion.div>

                      <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                        <Button
                          variant="outline"
                          className="border-cyan-400 text-cyan-400 hover:bg-cyan-400/10"
                          onClick={() => window.open(selectedProject.githubLink, "_blank")}
                        >
                          <Github className="mr-2 h-4 w-4" />
                          View Code
                        </Button>
                      </motion.div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}
