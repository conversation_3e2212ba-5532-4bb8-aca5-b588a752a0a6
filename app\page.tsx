"use client"

import { useState, useEffect } from "react"

// Type definitions
interface Particle {
  id: number
  x: number
  y: number
  size: number
  duration: number
}

interface Project {
  id: number
  title: string
  category: string
  description: string
  longDescription: string
  image: string
  tags: string[]
  color: string
  Link?: string
  demoLink?: string
  githubLink: string
  featured: boolean
  year: string
  status: string
}
import { motion, AnimatePresence } from "framer-motion"
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import {
  Palette,
  Code,
  Video,
  Lightbulb,
  Github,
  Linkedin,
  Mail,
  ExternalLink,
  Download,
  Moon,
  Sun,
  Sparkles,
  Heart,
  Coffee,
  Music,
  Camera,
  Instagram,
  MapPin,
  Briefcase,
  GraduationCap,
  Star,
  Eye,
  ChevronDown,
  Menu,
  X,
  Trophy,
  GitBranch,
  Calendar,
  Users,
  Target,
  Zap,
} from "lucide-react"

// ============================================
// PERSONAL INFORMATION - EASY TO CUSTOMIZE
// ============================================
const PERSONAL_INFO = {
  name: "Manasvi Avadhanula",
  title: "Full-Stack Developer • AI/ML Engineer • UI/UX Designer",
  bio: "Final-year B.Tech CSE (AI/ML) student with hands-on experience in full-stack development, software engineering, and GenAI/AI-ML applications. Skilled in designing and deploying scalable web apps, intelligent systems, and user-friendly interfaces using modern tech stacks. Seeking roles in software engineering, full stack, or AI/GenAI domains to deliver impactful solutions.",
  location: "Faridabad, Haryana, India",
  email: "<EMAIL>",
  phone: "+91-9958582766",
  developerResumeLink: "https://drive.google.com/file/d/1lRiZT2tum6Gbr0hfGUN5dQ_YFRm7v9eB/view?usp=sharing", // Add your developer resume link here
  designerResumeLink: "https://drive.google.com/file/d/1iGwX9JuOGv_ubXFU75K9drKIRwrpz_rt/view?usp=sharing", // Add your designer resume link here
  profileImage: "/placeholder.svg?height=200&width=200", // Replace with your photo URL
}

// ============================================
// SOCIAL LINKS - UPDATE WITH YOUR PROFILES
// ============================================
const SOCIAL_LINKS = {
  github: "https://github.com/manasvi0109",
  gitlab: "https://gitlab.com/manasvi010903",
  linkedin: "https://www.linkedin.com/in/manasvi-avadhanula-55856723a/",
  instagram: "https://instagram.com/manasviavadhanula",
  email: "mailto:<EMAIL>",
}

// ============================================
// SKILLS SECTION - ADD YOUR SKILLS
// ============================================
const SKILLS = [
  // Programming Languages
  { name: "Python", icon: Code, color: "from-blue-400 to-blue-600", level: 90, category: "Programming" },
  { name: "JavaScript", icon: Code, color: "from-yellow-400 to-orange-500", level: 88, category: "Programming" },
  { name: "Java", icon: Code, color: "from-red-400 to-red-600", level: 82, category: "Programming" },
  { name: "C++", icon: Code, color: "from-purple-400 to-purple-600", level: 80, category: "Programming" },

  // Web Development
  { name: "React", icon: Code, color: "from-cyan-400 to-blue-500", level: 92, category: "Web Development" },
  { name: "Node.js", icon: Code, color: "from-green-400 to-green-600", level: 88, category: "Web Development" },
  { name: "Django", icon: Code, color: "from-green-600 to-green-800", level: 85, category: "Web Development" },
  { name: "HTML/CSS", icon: Code, color: "from-orange-400 to-red-500", level: 95, category: "Web Development" },
  { name: "REST APIs", icon: Code, color: "from-indigo-400 to-purple-500", level: 87, category: "Web Development" },

  // Databases
  { name: "MySQL", icon: Code, color: "from-blue-500 to-blue-700", level: 85, category: "Database" },
  { name: "MongoDB", icon: Code, color: "from-green-500 to-green-700", level: 83, category: "Database" },
  { name: "PostgreSQL", icon: Code, color: "from-blue-600 to-indigo-600", level: 80, category: "Database" },

  // AI/ML
  { name: "Scikit-learn", icon: Lightbulb, color: "from-orange-400 to-orange-600", level: 88, category: "AI/ML" },
  { name: "NumPy", icon: Lightbulb, color: "from-blue-400 to-blue-600", level: 90, category: "AI/ML" },
  { name: "Pandas", icon: Lightbulb, color: "from-purple-400 to-purple-600", level: 92, category: "AI/ML" },
  {
    name: "GenAI & Prompt Engineering",
    icon: Lightbulb,
    color: "from-pink-400 to-rose-500",
    level: 89,
    category: "AI/ML",
  },
  { name: "Deep Learning", icon: Lightbulb, color: "from-violet-400 to-purple-500", level: 82, category: "AI/ML" },

  // Design Tools
  { name: "Figma", icon: Palette, color: "from-purple-400 to-pink-500", level: 93, category: "Design" },
  { name: "Adobe Premiere Pro", icon: Video, color: "from-purple-600 to-blue-600", level: 91, category: "Design" },
  { name: "After Effects", icon: Video, color: "from-blue-600 to-purple-600", level: 88, category: "Design" },
  { name: "Photoshop", icon: Palette, color: "from-blue-500 to-cyan-500", level: 85, category: "Design" },
  { name: "Illustrator", icon: Palette, color: "from-orange-400 to-red-500", level: 83, category: "Design" },
  { name: "Canva", icon: Palette, color: "from-purple-400 to-pink-400", level: 95, category: "Design" },
  { name: "DaVinci Resolve", icon: Video, color: "from-red-400 to-orange-500", level: 80, category: "Design" },
  { name: "Adobe XD", icon: Palette, color: "from-pink-400 to-purple-500", level: 87, category: "Design" },

  // Cloud & DevOps
  { name: "AWS", icon: Code, color: "from-orange-400 to-yellow-500", level: 86, category: "Cloud & DevOps" },
  { name: "Azure", icon: Code, color: "from-blue-400 to-blue-600", level: 82, category: "Cloud & DevOps" },
  { name: "Google Cloud", icon: Code, color: "from-red-400 to-yellow-400", level: 84, category: "Cloud & DevOps" },
  { name: "Linux", icon: Code, color: "from-gray-600 to-gray-800", level: 85, category: "Cloud & DevOps" },
  { name: "Git/GitHub", icon: Code, color: "from-gray-700 to-black", level: 92, category: "Cloud & DevOps" },

  // Other Tools
  { name: "VS Code", icon: Code, color: "from-blue-500 to-blue-700", level: 95, category: "Tools" },
  { name: "Arduino/IoT", icon: Code, color: "from-teal-400 to-cyan-500", level: 78, category: "Tools" },
  { name: "Blockchain", icon: Code, color: "from-yellow-400 to-orange-500", level: 75, category: "Tools" },
]

// ============================================
// PROJECTS SECTION - ADD YOUR PROJECTS
// ============================================
const PROJECTS = [
  {
    id: 1,
    title: "💊 MediKey – Universal Digital Medical Record System",
    category: "Ai/ML + Web Development",
    description:
      "A full-stack AI-powered platform integrated with blockchain to securely manage and share patient health records. Features include emergency QR access, patient-doctor dashboards, AI-based symptom checker, and encrypted record exchange. Built using React, Node.js, PostreSQL, and Ai/ML smart contracts.",
    longDescription:
      "MediKey is a secure, decentralized medical record management system built to streamline healthcare data accessibility in emergencies and daily care. The system allows patients and doctors to register, manage, and securely share health records using blockchain for integrity and privacy. A unique QR code enables emergency responders to access critical patient data instantly without login barriers. It integrates an AI-based symptom checker that guides users to possible conditions and directs them to relevant care. The platform also supports digital prescriptions, health history, and doctor recommendations — making it a one-stop healthcare solution. Designed with a modern UI/UX and mobile responsiveness in mind.",
    image: "/placeholder.svg?height=400&width=600",
    tags: ["React Native", "AIML", "Blockchain", "Medical Records", "Mobile App","Healthcare"],
    color: "from-green-400 to-emerald-500",
    Link: "https://medikey.vercel.app/",
    githubLink: "https://github.com/manasvi0109/medikey",
    featured: true,
    year: "2025",
    status: "In Progress",
  },
  {
    id: 2,
    title: "🤖 Questgen – Automatic Question Generator",
    category: "AI + Web Development",
    description:
      "An NLP-based tool that generates MCQs and short answer questions from input text using transformer models. Useful for educators, students, and e-learning platforms. Built with Python and Hugging Face.",
    longDescription:
      "Questgen is an AI tool designed to generate Multiple Choice Questions (MCQs) and short-answer quizzes from any educational text content. It leverages transformer-based NLP models (like T5) for extracting question-worthy sentences and framing relevant questions and distractors. This project can be used by teachers, test-prep platforms, and e-learning services to automate content creation. A Flask backend processes the input and returns the questions, making it easily integrable with other platforms or learning management systems (LMS).",
    image: "/placeholder.svg?height=400&width=600",
    tags: ["React", "AI/ML", "Data Visualization", "Dashboard", "Web Development", "Question Generation"],
    color: "from-blue-400 to-cyan-500",
    demoLink: "#",
    githubLink: "https://github.com/manasvi0109/questgen",
    featured: true,
    year: "2024",
    status: "Completed",
  },
  {
    id: 3,
    title: "🔍 TextSense – Real-Time Text Sentiment Analyzer",
    category: "AI + Web Development + Cloud",
    description:
      "A web app that analyzes text sentiment and emotion in real-time using NLP techniques. Built with Python, Streamlit, and Hugging Face models, aimed at content moderation, mental health monitoring, and customer feedback analysis.",
    longDescription:
      "TextSense is a powerful sentiment and emotion analysis tool built to evaluate text input in real-time. It can detect whether the input is positive, negative, or neutral, and also highlight emotions such as anger, joy, or sadness using transformer-based models. It can be used for analyzing tweets, product reviews, or mental health journal entries. This tool has potential use cases in customer support, feedback automation, and digital well-being. Built using Streamlit for a lightweight interface and Python NLP libraries for deep language analysis.",
    image: "/placeholder.svg?height=400&width=600",
    tags: ["React", "AI/ML", "Streamlit", "Azure", "Web Development", "Sentiment Analysis"],
    color: "from-blue-400 to-cyan-500",
    demoLink: "#",
    githubLink: "https://github.com/manasvi0109/vivevocal",
    featured: false,
    year: "2024",
    status: "Completed",
  },
  {
    id: 4,
    title: "🚨 Reach At Me – IoT-Based Accident Alert System for Two-Wheelers",
    category: "IOT + Video",
    description:
      "An IoT-powered accident detection and alert system designed for two-wheelers. It automatically detects crashes using sensors and sends real-time alerts with location data to emergency contacts, ensuring faster rescue in critical moments.",
    longDescription:
      "Reach At Me is an innovative IoT-based accident alert system specifically built to improve road safety for two-wheeler riders. The system uses a combination of motion and vibration sensors (like the MPU6050) to detect sudden impacts or abnormal motion patterns indicative of an accident. Upon detecting a crash, the device sends an emergency alert message containing the rider's real-time GPS location via GSM module (SIM-based) to pre-registered emergency contacts, including family or nearby hospitals.",
    image: "/placeholder.svg?height=400&width=600",
    tags: ["IOT", "AI/ML", "Data Visualization", "Sensors", "Emergency Contacts", "Arduino"],
    color: "from-blue-400 to-cyan-500",
    demoLink: "#",
    githubLink: "https://github.com/manasvi0109/RESCUE-US-NOW-R.U.N-",
    featured: false,
    year: "2022",
    status: "Completed",
  },
  {
    id: 5,
    title: "🌾 Agro-Z-Mine – Smart Crop Recommendation System",
    category: "AI + Cloud + IOT",
    description:
    "An AI-driven system to assist farmers with intelligent crop planning based on soil type, season, and location using machine learning algorithms. Includes a user-friendly frontend and visual data analysis using Python, Pandas, and Scikit-learn.",
    longDescription:
    "Agro-Z-Mine is a smart farming assistant that helps Indian farmers choose the best crops based on environmental conditions like soil type, region, and season. It uses historical datasets and predictive machine learning models to recommend suitable crops that will yield the best output. The solution addresses the lack of agricultural awareness in rural areas and promotes smart farming using AI. The app provides a lightweight UI for regional accessibility and uses Streamlit to visualize predictions. It was developed during a hackathon focused on solving rural development problems.",
    image: "/placeholder.svg?height=400&width=600",
    tags: ["AI/ML", "Cloud", "IOT", "Data Visualization", "Crop Prediction"],
    color: "from-blue-400 to-cyan-500",
    demoLink: "#",
    githubLink: "https://github.com/ASabhijeet/AGRO-Z-MINE",
    featured: false,
    year: "2023",
    status: "Completed",
    },
    {
      id: 6,
      title: "🏖️ Staycation.com – Travel Booking Website UI",
      category: "UI/UX + Design",
      description:
      "A user-friendly travel booking platform designed to help users find the perfect local staycation spots with ease and excitement.",
      longDescription:
      "Staycation.com is a vibrant and intuitive travel booking platform tailored for local explorations. The interface allows users to easily browse and book nearby getaways, view amenities, and read reviews—all in one place. With a relaxing color scheme and modern design layout, this project ensures users experience a vacation feel right from the homepage. The user journey focuses on fast search, easy navigation, and mobile responsiveness, making travel planning effortless and enjoyable.",
      image: "/placeholder.svg?height=400&width=600",
      tags: ["UI/UX", "Figma", "Travel Site", "Bookings"],
      color: "from-blue-400 to-cyan-500",
      demoLink: "https://www.figma.com/proto/IbRee5OYQt2znHERGHuw5j/staycation.com?t=mXx0fu3vx520wMVb-1",
      githubLink: "#",
      featured: false,
      year: "2025",
      status: "Completed",
    },
    {
      id: 7,
      title: "📈 MarketMind – Business Analytics Dashboard",
      category: "UI/UX + Design",
      description:
      "A clean and informative analytics dashboard designed for tracking market trends, user engagement, and sales metrics.",
      longDescription:
      "MarketMind is a sleek business dashboard UI built for professionals to visualize data effectively. The design prioritizes clarity with interactive graphs, charts, KPIs, and filters that enhance usability for data-driven decision-making. With a dark/light toggle theme, modern typography, and responsive grid layout, the dashboard offers a seamless experience across desktop and mobile devices. This was a team project that involved collaborative UX strategy and design thinking.",
      image: "/placeholder.svg?height=400&width=600",
      tags: ["UI/UX", "Figma", "Marketing", "Research and Development"],
      color: "from-blue-400 to-cyan-500",
      demoLink: "https://www.figma.com/proto/MSPE6xCtiMJdsPOZH7zxlx/MarketMind---Teamarcs-project?node-id=2-785&t=6W1OmwJ7J6ZkDKfm-1",
      githubLink: "#",
      featured: false,
      year: "2025",
      status: "Completed",
    },
    {
      id: 8,
      title: "🛍️ E-Commerce App UI",
      category: "UI/UX + Design",
      description:
      "A comprehensive mobile-first shopping app UI designed to offer seamless browsing, checkout, and product discovery.",
      longDescription:
      "The E-Commerce App UI combines elegance with function, focusing on making the shopping experience smooth and enjoyable. It features a smart product grid, intuitive filtering system, responsive navigation, and user-centric cart and checkout flows. Each screen emphasizes clarity and conversion optimization. Designed with mobile-first principles, the app ensures users can shop effortlessly from any device. Minimalist icons and soft color palettes elevate the user experience.",
      image: "/placeholder.svg?height=400&width=600",
      tags: ["UI/UX", "Figma", "E-Commerce", "Shopping"],
      color: "from-blue-400 to-cyan-500",
      demoLink: "https://www.figma.com/proto/GqfMfUUbekvt5KXtATpKWh/E-commerce-app?node-id=0-1&t=4FL437VFpN9ozbZO-1",
      githubLink: "#",
      featured: false,
      year: "2024",
      status: "Completed",
    },
    {
      id: 9,
      title: "🧘‍♀️ Health & Fitness Tracker UI",
      category: "UI/UX + Design",
      description:
      "A holistic fitness and health tracking app interface that motivates users to achieve their wellness goals.",
      longDescription:
      "This Health & Fitness Tracker app UI is designed for users to monitor physical activity, nutrition, hydration, sleep, and wellness routines. The visual design promotes calmness and positivity, featuring a progress dashboard, personalized reminders, and motivational badges. Key highlights include step counters, workout schedules, and diet logs—all presented in a clean and interactive interface. Accessibility and user motivation were key pillars of this design.",
      image: "/placeholder.svg?height=400&width=600",
      tags: ["UI/UX", "Figma", "Health & Fitness", "Wellness"],
      color: "from-blue-400 to-cyan-500",
      demoLink: "https://www.figma.com/proto/lRMWWviUIvVzmJnaIuuTOh/health-and-fitness-tracker?t=4FL437VFpN9ozbZO-1",
      githubLink: "#",
      featured: false,
      year: "2024",
      status: "In Progress",
    },
    {
      id: 10,
      title: "🍔 MunchIt – Food Ordering App UI",
      category: "UI/UX + Design",
      description:
      "An appealing food delivery and restaurant discovery app with a focus on convenience and visual delight.",
      longDescription:
      "MunchIt is a dynamic food ordering and delivery app that brings vibrant visuals and a hunger-inducing user experience. The interface is designed for easy navigation through food categories, restaurant listings, and customized meal selections. The app integrates real-time tracking, order history, and seamless payment options. Bright color themes and high-quality food images enhance user engagement, making every screen as tempting as the dishes themselves.",
      image: "/placeholder.svg?height=400&width=600",
      tags: ["UI/UX", "Figma", "Food Delivery", "Restaurant Discovery", "Food Ordering"],
      color: "from-blue-400 to-cyan-500",
      demoLink: "https://www.figma.com/proto/s0cHiNkufXyiH6emqNdWYD/munchit?t=4FL437VFpN9ozbZO-1",
      githubLink: "#",
      featured: false,
      year: "2024",
      status: "In Progress",
    },
    {
      id: 11,
      title: "💻 TechMantra – Tech Blog & Resource Hub",
      category: "Development",
      description:
      "A dynamic tech blog platform featuring curated articles, coding resources, and development guides for learners and tech enthusiasts.",
      longDescription:
      "TechMantra is a responsive and content-rich blog website tailored for the tech community. Designed with developers and learners in mind, it offers clean UI/UX for easy article navigation, category filtering, and topic-based content discovery. The project features a modern frontend built with React and styled using Tailwind CSS. It includes features like dark mode, responsive layouts, and intuitive navigation. Ideal for hosting technical write-ups, project showcases, and tutorials, TechMantra is a one-stop solution for knowledge sharing in tech.",
      image: "/placeholder.svg?height=400&width=600",
      tags: ["Full Stack", "Figma", "Web Development", "Tech Blogging", "Resource Hub"],
      color: "from-blue-400 to-cyan-500",
      demoLink: "https://techmantra.vercel.app/",
      githubLink: "https://github.com/manasvi0109/techmantra",
      featured: false,
      year: "2024",
      status: "In Progress",
    },
    {
      id: 12,
      title: "🎯 HiredIn – Job Portal Web App",
      category: "Development",
      description: "A feature-packed job portal web app connecting job seekers with recruiters through real-time applications and candidate tracking.",
      longDescription: "HiredIn is a full-stack recruitment web application that streamlines the hiring process for both candidates and companies. Built with modern frontend technologies, the app supports user authentication, job listings, application tracking, and real-time updates. It empowers recruiters to post jobs and manage applicants, while job seekers can create profiles and apply with a single click. The UI is clean, professional, and intuitive—offering seamless user experience across devices. HiredIn is built with scalability and performance in mind, perfect for startup HR platforms or portfolio showcases.",
      image: "/placeholder.svg?height=400&width=600",
      tags: ["Full Stack", "Figma", "Web Development", "Job Portal", "Recruitment"],
      color: "from-blue-400 to-cyan-500",
      demoLink: "https://hired-in-client.vercel.app/",
      githubLink: "https://github.com/manasvi0109/HiredIn",
      featured: false,
      year: "",
      status: "",
    },
    {
      id: 13,
      title: "All Other Frontend Projects",
      category: "Development + UI/UX + Design + Video",
      description: "All other frontend projects that I have worked on.",
      longDescription: "All other frontend projects that I have worked on.",
      image: "/placeholder.svg?height=400&width=600",
      tags: ["Frontend", "UI/UX", "Design"],
      color: "from-blue-400 to-cyan-500",
      githubLink: "https://github.com/manasvi0109?tab=repositories",
      featured: true,
      year: "",
      status: "",
    },
  // Add more projects here...
]

// ============================================
// EDUCATION SECTION - ADD YOUR EDUCATION
// ============================================
const EDUCATION = [
  {
    degree: "Bachelor of Technology (CSE in AI/ML Specialization)",
    institution: "Aravali College Of Engineering & Management",
    period: "Oct 2021 - June 2025",
    description:
      "Pursuing Bachelor's degree in Computer Science Engineering with specialization in Artificial Intelligence and Machine Learning. Focus on full-stack development, AI/ML applications, and software engineering principles.",
    gpa: "7.7/10 CGPA",
    relevant_courses: [
      "Data Structures & Algorithms",
      "Machine Learning",
      "Artificial Intelligence",
      "Database Management Systems",
      "Software Engineering",
      "Computer Networks",
      "Operating Systems",
      "Web Technologies",
      "Deep Learning",
      "Natural Language Processing",
    ],
    achievements: [
      "Cultural Head for 2 consecutive years",
      "Lead Designer - Created 50+ posters and promotional content",
      "7x Hackathon Winner",
      "Active participant in tech fests and coding competitions",
      "All rounder award",
    ],
  },
  {
    degree: "Higher Secondary Education (Class 12th - CBSE)",
    institution: "D.A.V. Public School",
    period: "2020 - May 2021",
    description:
      "Science stream with Mathematics, Physics, Chemistry, and Computer Science. Strong foundation in analytical thinking and problem-solving.",
    gpa: "79%",
    relevant_courses: ["Mathematics", "Physics", "Chemistry", "Computer Science", "English"],
    achievements: [
      "Consistent academic performance",
      "Active participation in science exhibitions",
      "Member of computer science club",
      "Inter school Cricket League Champion",
    ],
  },
  {
    degree: "Secondary Education (Class 10th - CBSE)",
    institution: "D.A.V. Public School",
    period: "May 2018 - May 2019",
    description:
      "Comprehensive secondary education with focus on science and mathematics. Developed strong analytical and logical reasoning skills.",
    gpa: "87%",
    relevant_courses: ["Mathematics", "Science", "Social Science", "English", "Hindi"],
    achievements: [
      "School topper in Computer Science",
      "Participated in inter-school competitions",
      "Member of school tech club",
      "Inter House Basketball Champion",
    ],
  },
]

// ============================================
// WORK EXPERIENCE - ADD YOUR WORK EXPERIENCE
// ============================================
const WORK_EXPERIENCE = [
  // Tech Experience
  {
    title: "Software Trainer",
    company: "Codefeast",
    period: "Jan 2025 - June 2025",
    description:
      "Delivering hands-on training to 100+ learners, including working professionals, on web development, AI/ML, and Python. Conducting interactive sessions for startups on emerging technologies and AI integration.",
    technologies: ["Python", "Web Development", "AI/ML", "Training & Development"],
    type: "Training",
    category: "tech",
  },
  {
    title: "Full-Stack Developer",
    company: "Zidio Development",
    period: "Jul 2024 - Oct 2024",
    description:
      "Developed and deployed scalable full-stack applications using React and Node.js. Integrated RESTful APIs and improved user experience across web platforms, contributing to multiple client projects.",
    technologies: ["React", "Node.js", "REST APIs", "JavaScript", "MongoDB"],
    type: "Full-time",
    category: "tech",
  },
  {
    title: "AI/ML Research Assistant",
    company: "College Research Lab",
    period: "Aug 2023 - Dec 2023",
    description:
      "Worked on machine learning projects focusing on natural language processing and computer vision. Developed AI models for text analysis and image recognition with 90%+ accuracy.",
    technologies: ["Python", "TensorFlow", "Scikit-learn", "OpenCV", "NLP"],
    type: "Research",
    category: "tech",
  },
  // Design Experience
  {
    title: "Video Editor & Graphic Designer",
    company: "Aspect Ratio",
    period: "July 2024 - Dec 2024",
    description:
      "Edited real estate and school promotional reels and social media posts. Collaborated with marketing team to create engaging content and worked on social media marketing strategies to increase brand value.",
    technologies: ["Adobe Premiere Pro", "After Effects", "Photoshop", "Social Media Marketing"],
    type: "Full-time",
    category: "design",
  },
  {
    title: "Video Editor",
    company: "Systemic Altruism",
    period: "June 2024 - July 2024",
    description:
      "Edited videos for social media, corporate, and promotional content. Collaborated with marketing team to create engaging visual content using Adobe Premiere Pro and After Effects.",
    technologies: ["Adobe Premiere Pro", "After Effects", "Video Production"],
    type: "Contract",
    category: "design",
  },
  {
    title: "Video Editing Intern",
    company: "Seth Consultancy Services",
    period: "June 2023 - Oct 2023",
    description:
      "Edited videos for various clients, including promotional content, social media clips, and educational videos. Managed multiple projects simultaneously, ensuring timely delivery of all content.",
    technologies: ["Adobe Premiere Pro", "DaVinci Resolve", "Canva", "Project Management"],
    type: "Internship",
    category: "design",
  },
]

// ============================================
// HACKATHONS - ADD YOUR HACKATHON WINS
// ============================================
const HACKATHONS = [
  {
    name: "Smart India Hackathon 2024",
    position: "Volunteer",
    project: "Video Editing and Photography",
    description: "Managed and organized events in hackathon and helped in resolving problems.",
    date: "Sept 2023",
    prize: "-",
    team_size: 6,
  },
  {
    name: "Hack - O - Relay 1.0",
    position: "3rd Priz",
    project: "Reach At Me – IoT-Based Accident Alert System for Two-Wheelers",
    description: "Built an accident alert system app and device that won third place among 100+ teams.",
    date: "Apr 2023",
    prize: "Rs. 10,000",
    team_size: 4,
  },
  {
    name: "Science Carnival 2023",
    position: "2nd Prize",
    project: "Reach At Me – IoT-Based Accident Alert System for Two-Wheelers",
    description: "Built an accident alert system app and device among several colleges all over India and won second prize.",
    date: "May 2023",
    prize: "Rs. 2,000",
    team_size: 4,
  },
  {
    name: "Innovative Technical Project Exhibition 2023",
    position: "1st Prize",
    project: "Reach At Me – IoT-Based Accident Alert System for Two-Wheelers",
    description: "Built an accident alert system device and showcaed among various professors and science experts and won first prize.",
    date: "June 2023",
    prize: "Rs. 5,000",
    team_size: 5,
  },
  {
    name: "Technothon 2.0",
    position: "Top 10 finalist",
    project: "Reach At Me – IoT-Based Accident Alert System for Two-Wheelers",
    description: "Built an accident alert system device and showcased among various faculty members from all over India.",
    date: "July 2023",
    prize: "-",
    team_size: 3,
  },
  {
    name: "Sports Meet",
    position: "2nd Prize",
    project: "Videography and Photography",
    description: "Captured live moments and candid pictures also participated in tug of war and won.",
    date: "Feb 2024",
    prize: "Rs. 1,000",
    team_size: 1,
  },
  {
    name: "Hack - O - Relay 2.0",
    position: "1st Place",
    project: "Agro-Z-Mine – Smart Crop Recommendation System",
    description: "Agro-Z-Mine is a smart farming assistant that helps Indian farmers choose the best crops and won 1st prize.",
    date: "Apr 2024",
    prize: "Rs. 15,000",
    team_size: 4,
  },
  {
    name: "HackSplash 2024",
    position: "Winner",
    project: "Rescue Us Now",
    description: "Built AI-powered accident alert system for two wheelers.",
    date: "May 2024",
    prize: "Rs. 5,000",
    team_size: 2,
  },
  {
    name: "Build With Delhi 2024",
    position: "Top 5",
    project: "Rescue Use Now",
    description: "Built AI-powered accident alert system for two wheelers.",
    date: "June 2024",
    prize: "Rs. 2,000",
    team_size: 4,
    },
]

// ============================================
// ACHIEVEMENTS - ADD YOUR ACHIEVEMENTS
// ============================================
const ACHIEVEMENTS = [
  {
    year: "2022-2024",
    title: "7x Hackathon Winner",
    description: "Recognized for innovation and teamwork across multiple national and international hackathons",
    icon: "🏆",
    category: "Competition",
  },
  {
    year: "2023-2024",
    title: "Cultural Head (2 Years)",
    description: "Led large-scale college events and tech fests, managing teams of 50+ students",
    icon: "👥",
    category: "Leadership",
  },
  {
    year: "2021-2025",
    title: "Lead Designer",
    description: "Created 50+ posters, promotional videos, and branding content for college events",
    icon: "🎨",
    category: "Design",
  },
  {
    year: "2024",
    title: "AWS Certified",
    description: "AWS Generative AI & DevOps Learning Path certification",
    icon: "☁️",
    category: "Certification",
  },
  {
    year: "2024-2025",
    title: "Google Cloud Certified",
    description: "Google Cloud Fundamentals certification with hands-on project experience",
    icon: "🌐",
    category: "Certification",
  },
  {
    year: "2024",
    title: "Microsoft AI Certified",
    description: "Microsoft Certified Generative AI Career Essentials in association with LinkedIn",
    icon: "🤖",
    category: "Certification",
  },
  {
    year: "2024",
    title: "Microsoft Azure Certified In Association With Infosys",
    description: "Microsoft Certified Artificial Intelligence Fundamentals",
    icon: "🤖",
    category: "Certification",
  },
  {
    year: "2024",
    title: "IBM Certified Prompt Engineering Fundamentals",
    description: "IBM Certified Generative AI and Prompt Engineering Fundamentals.",
    icon: "🤖",
    category: "Certification",
  },

]

// ============================================
// FUN FACTS - ADD YOUR PERSONAL TOUCHES
// ============================================
const FUN_FACTS = [
  { icon: Coffee, text: "Powered by movies and creativity" },
  { icon: Music, text: "Designs better with dance beats and part time pianist." },
  { icon: Camera, text: "Weekend photographer & videographer" },
]

// ============================================
// ANIMATED BACKGROUND COMPONENT
// ============================================
const AnimatedBackground = () => {
  const [particles, setParticles] = useState<Particle[]>([])

  useEffect(() => {
    const newParticles = []
    for (let i = 0; i < 50; i++) {
      newParticles.push({
        id: i,
        x: Math.random() * 100,
        y: Math.random() * 100,
        size: Math.random() * 4 + 1,
        duration: Math.random() * 20 + 10,
      })
    }
    setParticles(newParticles)
  }, [])

  return (
    <div className="fixed inset-0 overflow-hidden pointer-events-none z-0">
      {particles.map((particle) => (
        <motion.div
          key={particle.id}
          className="absolute rounded-full bg-gradient-to-r from-cyan-400 to-purple-500 opacity-20"
          style={{
            left: `${particle.x}%`,
            top: `${particle.y}%`,
            width: `${particle.size}px`,
            height: `${particle.size}px`,
          }}
          animate={{
            y: [-20, -100],
            opacity: [0, 0.6, 0],
          }}
          transition={{
            duration: particle.duration,
            repeat: Number.POSITIVE_INFINITY,
            ease: "linear",
          }}
        />
      ))}

      {/* Geometric shapes */}
      <div className="absolute inset-0">
        {[...Array(8)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute border border-cyan-400/20 rounded-lg"
            style={{
              left: `${Math.random() * 90}%`,
              top: `${Math.random() * 90}%`,
              width: `${Math.random() * 60 + 40}px`,
              height: `${Math.random() * 60 + 40}px`,
            }}
            animate={{
              rotate: [0, 360],
              scale: [1, 1.2, 1],
            }}
            transition={{
              duration: Math.random() * 10 + 10,
              repeat: Number.POSITIVE_INFINITY,
              ease: "linear",
            }}
          />
        ))}
      </div>
    </div>
  )
}

// ============================================
// MAIN PORTFOLIO COMPONENT
// ============================================
export default function Portfolio() {
  const [isDarkMode, setIsDarkMode] = useState(false)
  const [selectedProject, setSelectedProject] = useState<Project | null>(null)
  const [activeFilter, setActiveFilter] = useState("all")
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [activeSkillFilter, setActiveSkillFilter] = useState("all")

  // Filter projects based on active filter
  const filteredProjects = PROJECTS.filter((project) => {
    if (activeFilter === "all") return true
    if (activeFilter === "featured") return project.featured
    return project.category.toLowerCase().includes(activeFilter.toLowerCase())
  })

  const projectCategories = ["all", "featured", "ui/ux", "development", "video", "design"]

  // Scroll to section function
  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId)
    if (element) {
      element.scrollIntoView({ behavior: "smooth" })
      setIsMenuOpen(false)
    }
  }

  // Navigation items
  const navItems = [
    { label: "Home", id: "hero" },
    { label: "About", id: "about" },
    { label: "Skills", id: "skills" },
    { label: "Projects", id: "projects" },
    { label: "Education", id: "education" },
    { label: "Experience", id: "experience" },
    { label: "Hackathons", id: "hackathons" },
    { label: "Contact", id: "contact" },
  ]

  const skillCategories = [
    "all",
    "programming",
    "web development",
    "database",
    "ai/ml",
    "design",
    "cloud & devops",
    "tools",
  ]

  const filteredSkills = SKILLS.filter((skill) => {
    if (activeSkillFilter === "all") return true
    return skill.category.toLowerCase() === activeSkillFilter
  })

  return (
    <div
      className={`min-h-screen transition-all duration-500 ${isDarkMode ? "dark bg-slate-900" : "bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900"}`}
    >
      {/* Animated Background */}
      <AnimatedBackground />

      {/* Navigation */}
      <nav className="fixed top-0 left-0 right-0 z-50 backdrop-blur-xl bg-black/20 border-b border-cyan-500/20">
        <div className="max-w-7xl mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            {/* Logo/Brand - Removed text, just icon */}
            <motion.div initial={{ opacity: 0, x: -20 }} animate={{ opacity: 1, x: 0 }} className="flex items-center">
              <div className="w-10 h-10 rounded-lg bg-gradient-to-br from-cyan-400 to-purple-500 flex items-center justify-center">
                <Zap className="h-6 w-6 text-white" />
              </div>
            </motion.div>

            {/* Desktop Navigation */}
            <div className="hidden lg:flex items-center space-x-8">
              {navItems.map((item) => (
                <motion.button
                  key={item.id}
                  className="text-gray-300 hover:text-cyan-400 transition-all duration-300 font-medium relative group"
                  onClick={() => scrollToSection(item.id)}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  {item.label}
                  <span className="absolute inset-x-0 bottom-0 h-0.5 bg-gradient-to-r from-cyan-400 to-purple-500 scale-x-0 group-hover:scale-x-100 transition-transform duration-300" />
                </motion.button>
              ))}
            </div>

            {/* Theme Toggle & Mobile Menu */}
            <div className="flex items-center space-x-3">
              <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => setIsDarkMode(!isDarkMode)}
                  className="rounded-full bg-white/10 backdrop-blur-sm hover:bg-white/20 border border-cyan-500/30"
                >
                  {isDarkMode ? <Sun className="h-5 w-5 text-cyan-400" /> : <Moon className="h-5 w-5 text-cyan-400" />}
                </Button>
              </motion.div>

              <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                <Button
                  className="bg-gradient-to-r from-cyan-500 to-purple-600 hover:from-cyan-600 hover:to-purple-700 text-white rounded-full px-6 hidden sm:flex border-0 shadow-lg shadow-cyan-500/25"
                  onClick={() => window.open(PERSONAL_INFO.developerResumeLink, "_blank")}
                >
                  <Download className="mr-2 h-4 w-4" />
                  Resume
                </Button>
              </motion.div>

              {/* Mobile Menu Toggle */}
              <Button
                variant="ghost"
                size="icon"
                className="lg:hidden rounded-full bg-white/10 backdrop-blur-sm hover:bg-white/20 border border-cyan-500/30"
                onClick={() => setIsMenuOpen(!isMenuOpen)}
              >
                {isMenuOpen ? <X className="h-5 w-5 text-cyan-400" /> : <Menu className="h-5 w-5 text-cyan-400" />}
              </Button>
            </div>
          </div>

          {/* Mobile Navigation */}
          <AnimatePresence>
            {isMenuOpen && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: "auto" }}
                exit={{ opacity: 0, height: 0 }}
                className="lg:hidden mt-4 pb-4 border-t border-cyan-500/20"
              >
                <div className="flex flex-col space-y-3 pt-4">
                  {navItems.map((item) => (
                    <motion.button
                      key={item.id}
                      className="text-left text-gray-300 hover:text-cyan-400 transition-colors py-2 font-medium"
                      onClick={() => scrollToSection(item.id)}
                      whileHover={{ x: 10 }}
                    >
                      {item.label}
                    </motion.button>
                  ))}
                  <Button
                    className="bg-gradient-to-r from-cyan-500 to-purple-600 hover:from-cyan-600 hover:to-purple-700 text-white rounded-full mt-4 border-0"
                    onClick={() => window.open(PERSONAL_INFO.developerResumeLink, "_blank")}
                  >
                    <Download className="mr-2 h-4 w-4" />
                    Download Resume
                  </Button>
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </nav>

      {/* Hero Section */}
      <section id="hero" className="min-h-screen flex items-center justify-center pt-20 px-6 relative z-10">
        <div className="max-w-6xl mx-auto text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="mb-8"
          >
            {/* Profile Image */}
            <motion.div
              className="w-40 h-40 mx-auto mb-8 rounded-full p-1 bg-gradient-to-br from-cyan-400 via-purple-500 to-pink-500"
              whileHover={{ scale: 1.05 }}
              transition={{ duration: 0.3 }}
            >
              <div className="w-full h-full rounded-full bg-slate-900 p-2">
                <img
                  src={PERSONAL_INFO.profileImage || "/placeholder.svg"}
                  alt="Profile"
                  className="w-full h-full rounded-full object-cover border-2 border-cyan-400/30"
                />
              </div>
            </motion.div>

            <motion.h1
              className="text-5xl md:text-7xl font-bold mb-6"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
            >
              <span className="bg-gradient-to-r from-cyan-400 via-purple-400 to-pink-400 bg-clip-text text-transparent">
                Hi, I'm {PERSONAL_INFO.name}
              </span>
            </motion.h1>

            <motion.p
              className="text-xl md:text-2xl text-gray-300 mb-8 max-w-3xl mx-auto"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.4 }}
            >
              Designing with purpose, building with impact.
              <br />
              <span className="text-transparent bg-gradient-to-r from-cyan-400 to-purple-400 bg-clip-text font-semibold">
                {PERSONAL_INFO.title}
              </span>
            </motion.p>
          </motion.div>

          {/* CTA Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-12">
            <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
              <Button
                size="lg"
                className="bg-gradient-to-r from-cyan-500 to-purple-600 hover:from-cyan-600 hover:to-purple-700 text-white rounded-full px-8 py-3 border-0 shadow-lg shadow-cyan-500/25"
                onClick={() => scrollToSection("projects")}
              >
                <Sparkles className="mr-2 h-5 w-5" />
                View My Work
              </Button>
            </motion.div>

            <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
              <Button
                variant="outline"
                size="lg"
                className="border-cyan-400 text-cyan-400 hover:bg-cyan-400/10 backdrop-blur-sm rounded-full px-8 py-3"
                onClick={() => scrollToSection("contact")}
              >
                Let's Connect
              </Button>
            </motion.div>
          </div>

          {/* Resume Download Options */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.7 }}
            className="flex flex-col sm:flex-row gap-3 justify-center items-center mb-8"
          >
            <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
              <Button
                variant="outline"
                className="border-purple-400 text-purple-400 hover:bg-purple-400/10 backdrop-blur-sm rounded-full px-6 py-2"
                onClick={() => window.open(PERSONAL_INFO.developerResumeLink, "_blank")}
              >
                <Download className="mr-2 h-4 w-4" />
                Developer Resume
              </Button>
            </motion.div>

            <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
              <Button
                variant="outline"
                className="border-pink-400 text-pink-400 hover:bg-pink-400/10 backdrop-blur-sm rounded-full px-6 py-2"
                onClick={() => window.open(PERSONAL_INFO.designerResumeLink, "_blank")}
              >
                <Download className="mr-2 h-4 w-4" />
                Designer Resume
              </Button>
            </motion.div>
          </motion.div>

          {/* Social Links */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.8 }}
            className="flex justify-center gap-4"
          >
            {Object.entries(SOCIAL_LINKS).map(([platform, url]) => {
              const icons: Record<string, any> = {
                github: Github,
                gitlab: GitBranch,
                linkedin: Linkedin,
                instagram: Instagram,
                email: Mail,
              }
              const Icon = icons[platform]
              return (
                <motion.button
                  key={platform}
                  whileHover={{ scale: 1.1, y: -2 }}
                  whileTap={{ scale: 0.95 }}
                  className="p-3 rounded-full bg-white/10 backdrop-blur-sm border border-cyan-500/30 hover:border-cyan-400/50 hover:bg-white/20 transition-all duration-300"
                  onClick={() => window.open(url, "_blank")}
                >
                  <Icon className="h-5 w-5 text-gray-300 hover:text-cyan-400 transition-colors" />
                </motion.button>
              )
            })}
          </motion.div>

          {/* Scroll Indicator */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 1, delay: 1 }}
            className="absolute bottom-8 left-1/2 transform -translate-x-1/2"
          >
            <motion.div
              animate={{ y: [0, 10, 0] }}
              transition={{ duration: 2, repeat: Number.POSITIVE_INFINITY }}
              className="text-cyan-400/60"
            >
              <ChevronDown className="h-6 w-6" />
            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* About Me Section */}
      <section id="about" className="py-20 px-6 relative z-10">
        <div className="max-w-4xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl md:text-5xl font-bold mb-6 bg-gradient-to-r from-cyan-400 to-purple-500 bg-clip-text text-transparent">
              About Me
            </h2>
          </motion.div>

          <div className="grid md:grid-cols-2 gap-12 items-center">
            <motion.div
              initial={{ opacity: 0, x: -30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
            >
              <div className="w-64 h-64 mx-auto rounded-3xl bg-gradient-to-br from-cyan-400 via-purple-500 to-pink-500 p-1">
                <div className="w-full h-full rounded-3xl bg-slate-900 flex items-center justify-center">
                  <span className="text-8xl">👩‍🎨</span>
                </div>
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: 30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              className="space-y-6"
            >
              <p className="text-lg text-gray-300 leading-relaxed">{PERSONAL_INFO.bio}</p>

              <p className="text-lg text-gray-300 leading-relaxed">
                When I'm not crafting user experiences or building applications, you'll find me leading community
                initiatives, winning hackathons, or experimenting with the latest design trends and technologies.
              </p>

              <div className="space-y-4">
                <h3 className="text-xl font-semibold text-white mb-4">3 Fun Facts About Me:</h3>
                {FUN_FACTS.map((fact, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: index * 0.1 }}
                    whileHover={{ x: 10, scale: 1.02 }}
                    className="flex items-center gap-3 p-4 rounded-xl bg-white/5 backdrop-blur-sm border border-cyan-500/20 hover:border-cyan-400/40 transition-all duration-300 cursor-pointer"
                  >
                    <fact.icon className="h-6 w-6 text-cyan-400" />
                    <span className="text-gray-300">{fact.text}</span>
                  </motion.div>
                ))}
              </div>

              <div className="flex gap-3 pt-4">
                <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                  <Button
                    className="bg-gradient-to-r from-cyan-500 to-purple-600 hover:from-cyan-600 hover:to-purple-700 text-white border-0"
                    onClick={() => scrollToSection("contact")}
                  >
                    <Mail className="mr-2 h-4 w-4" />
                    Get In Touch
                  </Button>
                </motion.div>

                <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                  <Button
                    variant="outline"
                    className="border-cyan-400 text-cyan-400 hover:bg-cyan-400/10"
                    onClick={() => window.open(PERSONAL_INFO.developerResumeLink, "_blank")}
                  >
                    <Download className="mr-2 h-4 w-4" />
                    Resume
                  </Button>
                </motion.div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Skills Section */}
      <section id="skills" className="py-20 px-6 relative z-10">
        <div className="max-w-6xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl md:text-5xl font-bold mb-6 bg-gradient-to-r from-cyan-400 to-purple-500 bg-clip-text text-transparent">
              My Creative Toolkit
            </h2>
            <p className="text-xl text-gray-300 max-w-2xl mx-auto">
              Each skill is a carefully crafted tool in my digital studio, ready to bring ideas to life.
            </p>
          </motion.div>

          {/* Skills Filter */}
          <div className="flex flex-wrap justify-center gap-2 mb-8">
            {skillCategories.map((category) => (
              <motion.div key={category} whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                <Button
                  variant={activeSkillFilter === category ? "default" : "outline"}
                  size="sm"
                  onClick={() => setActiveSkillFilter(category)}
                  className={`capitalize ${
                    activeSkillFilter === category
                      ? "bg-gradient-to-r from-cyan-500 to-purple-600 text-white border-0"
                      : "border-cyan-400 text-cyan-400 hover:bg-cyan-400/10"
                  }`}
                >
                  {category}
                </Button>
              </motion.div>
            ))}
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredSkills.map((skill, index) => (
              <motion.div
                key={skill.name}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                whileHover={{ y: -8, scale: 1.02 }}
                className="group"
              >
                <Card className="h-full bg-white/5 backdrop-blur-sm border border-cyan-500/20 hover:border-cyan-400/40 shadow-lg hover:shadow-xl hover:shadow-cyan-500/10 transition-all duration-300">
                  <CardContent className="p-6">
                    <motion.div
                      className={`w-16 h-16 rounded-2xl bg-gradient-to-br ${skill.color} flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300`}
                      whileHover={{ rotate: [0, -10, 10, 0] }}
                      transition={{ duration: 0.5 }}
                    >
                      <skill.icon className="h-8 w-8 text-white" />
                    </motion.div>
                    <h3 className="text-xl font-semibold mb-2 text-white group-hover:text-cyan-400 transition-colors">
                      {skill.name}
                    </h3>
                    <div className="w-full bg-slate-700 rounded-full h-2 mb-2 overflow-hidden">
                      <motion.div
                        initial={{ width: 0 }}
                        whileInView={{ width: `${skill.level}%` }}
                        transition={{ duration: 1, delay: 0.5 }}
                        className={`h-2 rounded-full bg-gradient-to-r ${skill.color}`}
                      />
                    </div>
                    <span className="text-sm text-gray-400">{skill.level}% Proficiency</span>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Projects Section */}
      <section id="projects" className="py-20 px-6 relative z-10">
        <div className="max-w-6xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl md:text-5xl font-bold mb-6 bg-gradient-to-r from-cyan-400 to-purple-500 bg-clip-text text-transparent">
              Featured Projects
            </h2>
            <p className="text-xl text-gray-300 max-w-2xl mx-auto mb-8">
              A showcase of my favorite creations, where design meets functionality and innovation.
            </p>

            {/* Project Filter */}
            <div className="flex flex-wrap justify-center gap-2 mb-8">
              {projectCategories.map((category) => (
                <motion.div key={category} whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                  <Button
                    variant={activeFilter === category ? "default" : "outline"}
                    size="sm"
                    onClick={() => setActiveFilter(category)}
                    className={`capitalize ${
                      activeFilter === category
                        ? "bg-gradient-to-r from-cyan-500 to-purple-600 text-white border-0"
                        : "border-cyan-400 text-cyan-400 hover:bg-cyan-400/10"
                    }`}
                  >
                    {category}
                  </Button>
                </motion.div>
              ))}
            </div>
          </motion.div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <AnimatePresence>
              {filteredProjects.map((project, index) => (
                <motion.div
                  key={project.id}
                  initial={{ opacity: 0, y: 30 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: 30 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  whileHover={{ y: -8, scale: 1.02 }}
                  className="group"
                >
                  <Card className="h-full bg-white/5 backdrop-blur-sm border border-cyan-500/20 hover:border-cyan-400/40 shadow-lg hover:shadow-xl hover:shadow-cyan-500/10 transition-all duration-500 overflow-hidden">
                    <div className="relative overflow-hidden">
                      <motion.img
                        src={project.image || "/placeholder.svg"}
                        alt={project.title}
                        className="w-full h-48 object-cover group-hover:scale-110 transition-transform duration-500"
                        whileHover={{ scale: 1.1 }}
                      />
                      <div
                        className={`absolute top-4 left-4 px-3 py-1 rounded-full text-white text-sm font-medium bg-gradient-to-r ${project.color} backdrop-blur-sm`}
                      >
                        {project.category}
                      </div>
                      {project.featured && (
                        <div className="absolute top-4 right-4 px-3 py-1 rounded-full bg-yellow-500 text-white text-sm font-medium backdrop-blur-sm">
                          <Star className="inline h-3 w-3 mr-1" />
                          Featured
                        </div>
                      )}
                    </div>

                    <CardContent className="p-6">
                      <div className="flex justify-between items-start mb-2">
                        <h3 className="text-2xl font-bold text-white group-hover:text-cyan-400 transition-colors">
                          {project.title}
                        </h3>
                        <span className="text-sm text-gray-400">{project.year}</span>
                      </div>

                      <p className="text-gray-300 mb-4 leading-relaxed">{project.description}</p>

                      <div className="flex flex-wrap gap-2 mb-6">
                        {project.tags.map((tag) => (
                          <Badge
                            key={tag}
                            variant="secondary"
                            className="bg-cyan-500/10 text-cyan-400 border border-cyan-500/20 hover:bg-cyan-500/20 transition-colors"
                          >
                            {tag}
                          </Badge>
                        ))}
                      </div>

                      <div className="flex gap-3">
                        <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                          <Button
                            size="sm"
                            className="bg-gradient-to-r from-cyan-500 to-purple-600 hover:from-cyan-600 hover:to-purple-700 text-white border-0"
                            onClick={() => window.open(project.demoLink, "_blank")}
                          >
                            <ExternalLink className="mr-2 h-4 w-4" />
                            Live Demo
                          </Button>
                        </motion.div>

                        <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                          <Button
                            variant="outline"
                            size="sm"
                            className="border-cyan-400 text-cyan-400 hover:bg-cyan-400/10"
                            onClick={() => window.open(project.githubLink, "_blank")}
                          >
                            <Github className="mr-2 h-4 w-4" />
                            Code
                          </Button>
                        </motion.div>

                        <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                          <Button
                            variant="outline"
                            size="sm"
                            className="border-cyan-400 text-cyan-400 hover:bg-cyan-400/10"
                            onClick={() => setSelectedProject(project)}
                          >
                            <Eye className="mr-2 h-4 w-4" />
                            Details
                          </Button>
                        </motion.div>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </AnimatePresence>
          </div>

          {filteredProjects.length === 0 && (
            <div className="text-center py-12">
              <p className="text-gray-400">No projects found for the selected filter.</p>
            </div>
          )}
        </div>
      </section>

      {/* Education Section */}
      <section id="education" className="py-20 px-6 relative z-10">
        <div className="max-w-6xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl md:text-5xl font-bold mb-6 bg-gradient-to-r from-cyan-400 to-purple-500 bg-clip-text text-transparent">
              Education
            </h2>
            <p className="text-xl text-gray-300">My academic journey and qualifications.</p>
          </motion.div>

          <div className="space-y-8">
            {EDUCATION.map((edu, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, x: -30 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                whileHover={{ scale: 1.02, x: 10 }}
              >
                <Card className="bg-white/5 backdrop-blur-sm border border-cyan-500/20 hover:border-cyan-400/40 shadow-lg hover:shadow-xl hover:shadow-cyan-500/10 transition-all duration-300">
                  <CardContent className="p-6">
                    <div className="flex items-start gap-4">
                      <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-cyan-400 to-purple-500 flex items-center justify-center flex-shrink-0">
                        <GraduationCap className="h-6 w-6 text-white" />
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-2">
                          <h3 className="text-xl font-bold text-white">{edu.degree}</h3>
                          <Badge className="bg-cyan-500/20 text-cyan-400 border border-cyan-500/30">{edu.gpa}</Badge>
                        </div>
                        <p className="text-cyan-400 font-medium mb-2">{edu.institution}</p>
                        <p className="text-gray-300 mb-3">{edu.description}</p>
                        <div className="flex items-center gap-4">
                          <span className="text-sm text-cyan-400 font-medium flex items-center gap-1">
                            <Calendar className="h-4 w-4" />
                            {edu.period}
                          </span>
                        </div>
                        {edu.relevant_courses && (
                          <div className="mt-3">
                            <p className="text-sm text-gray-400 mb-2">Key Courses:</p>
                            <div className="flex flex-wrap gap-2">
                              {edu.relevant_courses.map((course, idx) => (
                                <Badge
                                  key={idx}
                                  variant="outline"
                                  className="text-xs border-purple-500/30 text-purple-400"
                                >
                                  {course}
                                </Badge>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Work Experience Section */}
      <section id="experience" className="py-20 px-6 relative z-10">
        <div className="max-w-6xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl md:text-5xl font-bold mb-6 bg-gradient-to-r from-cyan-400 to-purple-500 bg-clip-text text-transparent">
              Work Experience
            </h2>
            <p className="text-xl text-gray-300">My professional journey and contributions.</p>
          </motion.div>

          <div className="relative">
            <div className="absolute left-8 top-0 bottom-0 w-0.5 bg-gradient-to-b from-cyan-400 to-purple-600"></div>

            {WORK_EXPERIENCE.map((exp, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, x: -30 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                whileHover={{ scale: 1.02, x: 10 }}
                className="relative mb-8"
              >
                <div className="absolute left-6 w-4 h-4 bg-gradient-to-br from-cyan-400 to-purple-600 rounded-full border-4 border-slate-900"></div>

                <Card className="ml-16 bg-white/5 backdrop-blur-sm border border-cyan-500/20 hover:border-cyan-400/40 shadow-lg hover:shadow-xl hover:shadow-cyan-500/10 transition-all duration-300">
                  <CardContent className="p-6">
                    <div className="flex items-center gap-2 mb-2">
                      <Briefcase className="h-5 w-5 text-cyan-400" />
                      <Badge className="bg-cyan-500/20 text-cyan-400 border border-cyan-500/30">{exp.type}</Badge>
                      <span className="text-sm font-medium text-purple-400 ml-auto">{exp.period}</span>
                    </div>
                    <h3 className="text-xl font-bold mb-1 text-white">{exp.title}</h3>
                    <p className="text-cyan-400 font-medium mb-2">{exp.company}</p>
                    <p className="text-gray-300 mb-3">{exp.description}</p>
                    {exp.technologies && (
                      <div className="flex flex-wrap gap-2">
                        {exp.technologies.map((tech, idx) => (
                          <Badge key={idx} variant="outline" className="text-xs border-purple-500/30 text-purple-400">
                            {tech}
                          </Badge>
                        ))}
                      </div>
                    )}
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Hackathons Section */}
      <section id="hackathons" className="py-20 px-6 relative z-10">
        <div className="max-w-6xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl md:text-5xl font-bold mb-6 bg-gradient-to-r from-cyan-400 to-purple-500 bg-clip-text text-transparent">
              Hackathons & Competitions
            </h2>
            <p className="text-xl text-gray-300">Competitive programming and innovation challenges.</p>
          </motion.div>

          <div className="grid md:grid-cols-2 gap-8">
            {HACKATHONS.map((hackathon, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                whileHover={{ y: -8, scale: 1.02 }}
                className="group"
              >
                <Card className="h-full bg-white/5 backdrop-blur-sm border border-cyan-500/20 hover:border-cyan-400/40 shadow-lg hover:shadow-xl hover:shadow-cyan-500/10 transition-all duration-300">
                  <CardContent className="p-6">
                    <div className="flex items-start gap-4">
                      <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-yellow-400 to-orange-500 flex items-center justify-center flex-shrink-0">
                        <Trophy className="h-6 w-6 text-white" />
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-2">
                          <h3 className="text-xl font-bold text-white group-hover:text-cyan-400 transition-colors">
                            {hackathon.name}
                          </h3>
                          <Badge className="bg-yellow-500/20 text-yellow-400 border border-yellow-500/30">
                            {hackathon.position}
                          </Badge>
                        </div>
                        <p className="text-cyan-400 font-medium mb-2">{hackathon.project}</p>
                        <p className="text-gray-300 mb-3">{hackathon.description}</p>
                        <div className="flex items-center gap-4 text-sm text-gray-400">
                          <span className="flex items-center gap-1">
                            <Calendar className="h-4 w-4" />
                            {hackathon.date}
                          </span>
                          <span className="flex items-center gap-1">
                            <Users className="h-4 w-4" />
                            Team of {hackathon.team_size}
                          </span>
                          <span className="flex items-center gap-1">
                            <Target className="h-4 w-4" />
                            {hackathon.prize}
                          </span>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>

          {/* Achievements */}
          <div className="mt-16">
            <h3 className="text-2xl font-bold mb-8 text-white text-center">Other Achievements</h3>
            <div className="grid md:grid-cols-3 gap-6">
              {ACHIEVEMENTS.map((achievement, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  whileHover={{ scale: 1.05 }}
                >
                  <Card className="bg-white/5 backdrop-blur-sm border border-cyan-500/20 hover:border-cyan-400/40 shadow-lg hover:shadow-xl hover:shadow-cyan-500/10 transition-all duration-300">
                    <CardContent className="p-6 text-center">
                      <div className="text-4xl mb-3">{achievement.icon}</div>
                      <h3 className="text-lg font-bold text-white mb-2">{achievement.title}</h3>
                      <p className="text-gray-300 text-sm mb-3">{achievement.description}</p>
                      <div className="flex items-center justify-center gap-2">
                        <Badge variant="outline" className="text-xs border-cyan-500/30 text-cyan-400">
                          {achievement.category}
                        </Badge>
                        <span className="text-xs text-purple-400 font-medium">{achievement.year}</span>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section id="contact" className="py-20 px-6 relative z-10">
        <div className="max-w-4xl mx-auto text-center">
          <motion.div initial={{ opacity: 0, y: 30 }} whileInView={{ opacity: 1, y: 0 }} transition={{ duration: 0.8 }}>
            <h2 className="text-4xl md:text-5xl font-bold mb-6 bg-gradient-to-r from-cyan-400 to-purple-500 bg-clip-text text-transparent">
              Let's Create Together
            </h2>
            <p className="text-xl text-gray-300 mb-12 max-w-2xl mx-auto">
              Ready to bring your ideas to life? I'm always excited to collaborate on meaningful projects that make a
              difference. Let's connect and create something amazing!
            </p>

            <Card className="max-w-2xl mx-auto bg-white/5 backdrop-blur-sm border border-cyan-500/20 shadow-xl">
              <CardContent className="p-8">
                {/* Contact Info */}
                <div className="grid md:grid-cols-2 gap-6 mb-8">
                  <motion.div
                    className="flex items-center gap-3 p-4 rounded-lg bg-cyan-500/10 border border-cyan-500/20"
                    whileHover={{ scale: 1.05 }}
                  >
                    <Mail className="h-5 w-5 text-cyan-400" />
                    <div className="text-left">
                      <p className="text-sm text-gray-400">Email</p>
                      <p className="font-medium text-white">{PERSONAL_INFO.email}</p>
                    </div>
                  </motion.div>
                  <motion.div
                    className="flex items-center gap-3 p-4 rounded-lg bg-purple-500/10 border border-purple-500/20"
                    whileHover={{ scale: 1.05 }}
                  >
                    <MapPin className="h-5 w-5 text-purple-400" />
                    <div className="text-left">
                      <p className="text-sm text-gray-400">Location</p>
                      <p className="font-medium text-white">{PERSONAL_INFO.location}</p>
                    </div>
                  </motion.div>
                </div>

                <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-8">
                  <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                    <Button
                      size="lg"
                      className="bg-gradient-to-r from-cyan-500 to-purple-600 hover:from-cyan-600 hover:to-purple-700 text-white rounded-full px-8 border-0"
                      onClick={() => window.open(SOCIAL_LINKS.email, "_blank")}
                    >
                      <Mail className="mr-2 h-5 w-5" />
                      Send Email
                    </Button>
                  </motion.div>

                  <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                    <Button
                      variant="outline"
                      size="lg"
                      className="border-cyan-400 text-cyan-400 hover:bg-cyan-400/10 rounded-full px-8"
                      onClick={() => window.open(PERSONAL_INFO.developerResumeLink, "_blank")}
                    >
                      <Download className="mr-2 h-5 w-5" />
                      Download Resume
                    </Button>
                  </motion.div>
                </div>

                <div className="flex justify-center gap-4">
                  {Object.entries(SOCIAL_LINKS).map(([platform, url]) => {
                    const icons: Record<string, any> = {
                      github: Github,
                      gitlab: GitBranch,
                      linkedin: Linkedin,
                      instagram: Instagram,
                      email: Mail,
                    }
                    const Icon = icons[platform]
                    const colors: Record<string, string> = {
                      github: "hover:text-white",
                      gitlab: "hover:text-orange-400",
                      linkedin: "hover:text-blue-400",
                      instagram: "hover:text-pink-400",
                      email: "hover:text-cyan-400",
                    }
                    return (
                      <motion.button
                        key={platform}
                        whileHover={{ scale: 1.1, y: -2 }}
                        whileTap={{ scale: 0.95 }}
                        className={`p-4 rounded-full bg-white/5 backdrop-blur-sm border border-cyan-500/20 hover:border-cyan-400/40 text-gray-300 ${colors[platform]} transition-all duration-300`}
                        onClick={() => window.open(url, "_blank")}
                      >
                        <Icon className="h-6 w-6" />
                      </motion.button>
                    )
                  })}
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </section>

      {/* Footer */}
      <footer className="py-8 px-6 text-center text-gray-400 border-t border-cyan-500/20 relative z-10">
        <div className="max-w-6xl mx-auto">
          <p className="flex items-center justify-center gap-2 mb-4">
            Made with <Heart className="h-4 w-4 text-cyan-400" /> by {PERSONAL_INFO.name} • 2024
          </p>
          <p className="text-sm">Built with TailwindCSS & React.js</p>
        </div>
      </footer>

      {/* Project Detail Modal */}
      <AnimatePresence>
        {selectedProject && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 z-50 bg-black/80 backdrop-blur-sm flex items-center justify-center p-4"
            onClick={() => setSelectedProject(null)}
          >
            <motion.div
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.8, opacity: 0 }}
              onClick={(e) => e.stopPropagation()}
              className="max-w-4xl w-full max-h-[90vh] overflow-y-auto"
            >
              <Card className="bg-slate-900 border border-cyan-500/30 shadow-2xl">
                <CardContent className="p-0">
                  {/* Project Image */}
                  <div className="relative h-64 md:h-80">
                    <img
                      src={selectedProject.image || "/placeholder.svg"}
                      alt={selectedProject.title}
                      className="w-full h-full object-cover"
                    />
                    <div className="absolute top-4 right-4">
                      <Button
                        variant="outline"
                        size="icon"
                        onClick={() => setSelectedProject(null)}
                        className="bg-black/50 backdrop-blur-sm border-cyan-500/30 hover:bg-black/70"
                      >
                        <X className="h-4 w-4 text-white" />
                      </Button>
                    </div>
                    <div
                      className={`absolute bottom-4 left-4 px-3 py-1 rounded-full text-white text-sm font-medium bg-gradient-to-r ${selectedProject.color} backdrop-blur-sm`}
                    >
                      {selectedProject.category}
                    </div>
                  </div>

                  {/* Project Details */}
                  <div className="p-8">
                    <div className="flex justify-between items-start mb-4">
                      <h2 className="text-3xl font-bold text-white">{selectedProject.title}</h2>
                      <div className="flex items-center gap-2">
                        <Badge variant="outline" className="border-cyan-500/30 text-cyan-400">
                          {selectedProject.year}
                        </Badge>
                        <Badge variant="outline" className="border-green-500/30 text-green-400">
                          {selectedProject.status}
                        </Badge>
                      </div>
                    </div>

                    <p className="text-lg text-gray-300 mb-6 leading-relaxed">{selectedProject.longDescription}</p>

                    <div className="mb-6">
                      <h3 className="text-lg font-semibold mb-3 text-white">Technologies Used</h3>
                      <div className="flex flex-wrap gap-2">
                        {selectedProject.tags.map((tag: string) => (
                          <Badge
                            key={tag}
                            variant="secondary"
                            className="bg-cyan-500/10 text-cyan-400 border border-cyan-500/20"
                          >
                            {tag}
                          </Badge>
                        ))}
                      </div>
                    </div>

                    <div className="flex gap-4">
                      <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                        <Button
                          className="bg-gradient-to-r from-cyan-500 to-purple-600 hover:from-cyan-600 hover:to-purple-700 text-white border-0"
                          onClick={() => window.open(selectedProject.demoLink, "_blank")}
                        >
                          <ExternalLink className="mr-2 h-4 w-4" />
                          Live Demo
                        </Button>
                      </motion.div>

                      <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                        <Button
                          variant="outline"
                          className="border-cyan-400 text-cyan-400 hover:bg-cyan-400/10"
                          onClick={() => window.open(selectedProject.githubLink, "_blank")}
                        >
                          <Github className="mr-2 h-4 w-4" />
                          View Code
                        </Button>
                      </motion.div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}
